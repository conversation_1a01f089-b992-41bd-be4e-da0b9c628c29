# Supabase OTP 配置指南

为了让您的应用发送6位数验证码而不是魔法链接，您需要在Supabase Dashboard中修改邮件模板。

## 配置步骤

### 1. 登录 Supabase Dashboard
访问 [https://supabase.com/dashboard](https://supabase.com/dashboard) 并登录您的账户。

### 2. 选择您的项目
选择 `lenskiss` 项目（或您的项目名称）。

### 3. 修改邮件模板
1. 在左侧导航栏中，点击 **Authentication** > **Email Templates**
2. 找到 **Magic Link** 模板 (opt 用不到)
3. 将模板内容替换为以下内容：

```html
<h2>验证码登录</h2>
<p>您的验证码是：</p>
<h1 style="font-size: 32px; font-weight: bold; text-align: center; color: #333; background: #f5f5f5; padding: 20px; border-radius: 8px; letter-spacing: 8px;">{{ .Token }}</h1>
<p>此验证码将在10分钟后过期。</p>
<p>如果您没有请求此验证码，请忽略此邮件。</p>
```

### 4. 修改确认邮件模板（用于注册）
1. 找到 **Confirm signup** 模板
2. 将模板内容替换为以下内容：

```html
<h2>确认您的邮箱</h2>
<p>感谢您注册！您的验证码是：</p>
<h1 style="font-size: 32px; font-weight: bold; text-align: center; color: #333; background: #f5f5f5; padding: 20px; border-radius: 8px; letter-spacing: 8px;">{{ .Token }}</h1>
<p>请在应用中输入此验证码以完成注册。</p>
<p>此验证码将在10分钟内过期。</p>
```

### 5. 修改密码重置模板
1. 找到 **Reset Password** 模板
2. 将模板内容替换为以下内容：

```html
<h2>重置密码</h2>
<p>您请求重置密码。您的验证码是：</p>
<h1 style="font-size: 32px; font-weight: bold; text-align: center; color: #333; background: #f5f5f5; padding: 20px; border-radius: 8px; letter-spacing: 8px;">{{ .Token }}</h1>
<p>请在应用中输入此验证码以重置您的密码。</p>
<p>此验证码将在10分钟内过期。</p>
<p>如果您没有请求重置密码，请忽略此邮件。</p>
```

### 6. 保存更改
点击每个模板的 **Save** 按钮保存更改。

## 重要说明

1. **{{ .Token }}** 变量会被替换为6位数验证码
2. 验证码默认10分钟过期
3. 用户每60秒只能请求一次新的验证码
4. 修改模板后，新的邮件将发送验证码而不是魔法链接

## 测试
配置完成后，您可以：
1. 尝试注册新账户
2. 尝试重置密码
3. 检查邮箱是否收到6位数验证码而不是魔法链接

如果仍然收到魔法链接，请检查：
- 模板是否正确保存
- 是否使用了正确的 `{{ .Token }}` 变量
- 浏览器缓存是否已清除
