{"name": "<PERSON><PERSON>s", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy --minify", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload --minify", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,less,scss,md}\""}, "dependencies": {"@opennextjs/cloudflare": "^1.5.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.13", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jszip": "^3.10.1", "lucide-react": "^0.511.0", "next": "^15.3.4", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.4", "upng-js": "^2.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/jszip": "^3.4.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/upng-js": "^2.1.5", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5", "wrangler": "^4.25.0"}}