'use client';

import { useState, useCallback, useRef } from 'react';

export interface HistoryState<T> {
  state: T;
  timestamp: number;
  action?: string;
}

interface UseHistoryOptions {
  maxHistorySize?: number;
  debounceMs?: number;
}

export function useHistory<T>(
  initialState: T,
  options: UseHistoryOptions = {}
) {
  const { maxHistorySize = 50, debounceMs = 300 } = options;
  
  const [currentState, setCurrentState] = useState<T>(initialState);
  const [history, setHistory] = useState<HistoryState<T>[]>([
    { state: initialState, timestamp: Date.now(), action: 'initial' }
  ]);
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isDraggingRef = useRef(false);
  const pendingStateRef = useRef<T | null>(null);

  // 添加新的历史状态
  const pushState = useCallback((newState: T, action?: string, immediate = false) => {
    if (immediate || !isDraggingRef.current) {
      // 立即添加到历史记录
      setHistory(prev => {
        const newHistory = prev.slice(0, currentIndex + 1);
        newHistory.push({
          state: newState,
          timestamp: Date.now(),
          action
        });
        
        // 限制历史记录大小
        if (newHistory.length > maxHistorySize) {
          newHistory.shift();
          setCurrentIndex(prev => Math.max(0, prev - 1));
          return newHistory;
        }
        
        setCurrentIndex(newHistory.length - 1);
        return newHistory;
      });
    } else {
      // 拖拽期间，只更新待处理状态
      pendingStateRef.current = newState;
      
      // 清除之前的防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      
      // 设置新的防抖定时器
      debounceTimerRef.current = setTimeout(() => {
        if (pendingStateRef.current && !isDraggingRef.current) {
          pushState(pendingStateRef.current, action, true);
          pendingStateRef.current = null;
        }
      }, debounceMs);
    }
    
    setCurrentState(newState);
  }, [currentIndex, maxHistorySize, debounceMs]);

  // 开始拖拽
  const startDragging = useCallback(() => {
    isDraggingRef.current = true;
  }, []);

  // 结束拖拽
  const endDragging = useCallback(() => {
    isDraggingRef.current = false;
    
    // 如果有待处理的状态，立即添加到历史记录
    if (pendingStateRef.current) {
      pushState(pendingStateRef.current, 'slider_change', true);
      pendingStateRef.current = null;
    }
  }, [pushState]);

  // 撤销
  const undo = useCallback(() => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      setCurrentState(history[newIndex].state);
    }
  }, [currentIndex, history]);

  // 重做
  const redo = useCallback(() => {
    if (currentIndex < history.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      setCurrentState(history[newIndex].state);
    }
  }, [currentIndex, history]);

  // 重置到初始状态
  const reset = useCallback(() => {
    const resetState = { state: initialState, timestamp: Date.now(), action: 'reset' };
    setHistory([resetState]);
    setCurrentIndex(0);
    setCurrentState(initialState);
  }, [initialState]);

  // 清理定时器
  const cleanup = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }
  }, []);

  return {
    currentState,
    history,
    currentIndex,
    canUndo: currentIndex > 0,
    canRedo: currentIndex < history.length - 1,
    pushState,
    startDragging,
    endDragging,
    undo,
    redo,
    reset,
    cleanup
  };
}
