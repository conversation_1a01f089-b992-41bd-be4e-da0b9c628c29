'use client'

import { createClient } from '@/lib/supabase/client'
import { UserMetadata } from '@supabase/supabase-js'
import { useEffect, useState } from 'react'

export function useAuth() {
  const [user, setUser] = useState<UserMetadata>()
  const [loading, setLoading] = useState(true) // 初始状态设为 true，避免闪烁

  // 获取当前用户
  const getUser = async () => {
    const supabase = createClient()
    try {
      const { data } = await supabase.auth.getClaims()
      if (data?.claims) {
        setUser(data.claims.user_metadata)
      } else {
        setUser(undefined)
      }
    } catch (error) {
      setUser(undefined)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getUser()

    // // 监听认证状态变化
    // const supabase = createClient()
    // const {
    //   data: { subscription }
    // } = supabase.auth.onAuthStateChange(async (_event, session) => {
    //   if (session?.user) {
    //     setUser(session.user.user_metadata)
    //   } else {
    //     setUser(undefined)
    //   }
    //   setLoading(false)
    // })

    // return () => subscription.unsubscribe()
  }, [])

  const signOut = async () => {
    const supabase = createClient()
    await supabase.auth.signOut()
    window.location.reload()
  }

  return {
    user,
    loading,
    signOut
  }
}
