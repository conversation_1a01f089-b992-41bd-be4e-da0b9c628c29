import { LandingPage } from '@/types/pages/landing'
import { routing } from '@/i18n/routing'

const { locales, defaultLocale } = routing

export async function getLandingPage(locale: string): Promise<LandingPage> {
  // 检查是否支持该语言
  const targetLocale = locales.includes(locale as (typeof locales)[number]) ? locale : defaultLocale

  try {
    return await import(`@/messages/${targetLocale}.json`).then(module => module.default)
  } catch (error) {
    return await import(`@/messages/${defaultLocale}.json`).then(module => module.default)
  }
}
