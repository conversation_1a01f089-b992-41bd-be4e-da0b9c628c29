import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import ImageSplitter from '@/components/image-splitter/image-splitter';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'ImageSplitter' });

  return {
    title: t('title'),
    description: t('description'),
  };
}

export default async function ImageSplitterPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  return <ImageSplitter />;
}
