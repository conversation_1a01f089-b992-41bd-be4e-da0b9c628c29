import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import ImageEditor from '@/components/image-editor/image-editor';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'ImageEditor' });

  return {
    title: t('title'),
    description: t('description'),
  };
}

export default async function ImageEditorPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  setRequestLocale(locale);
  const t = await getTranslations({ locale, namespace: 'ImageEditor' });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        <ImageEditor />
      </div>
    </div>
  );
}
