import { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import GeometryEditor from '@/components/image-editor/geometry-editor';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'ImageEditor' });

  return {
    title: `${t('title')} - 几何变换`,
    description: '单独进行旋转、翻转与缩放等几何变换操作'
  };
}

export default async function GeometryEditorPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  setRequestLocale(locale);
  const t = await getTranslations({ locale, namespace: 'ImageEditor' });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        <GeometryEditor />
      </div>
    </div>
  );
}
