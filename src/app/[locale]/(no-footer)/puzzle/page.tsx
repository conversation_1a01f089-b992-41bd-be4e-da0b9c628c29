import { setRequestLocale } from 'next-intl/server';
import { getTranslations } from 'next-intl/server';
import PuzzleEditor from '@/components/puzzle/puzzle-editor';

export default async function PuzzlePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params;
  setRequestLocale(locale);
  const t = await getTranslations({ locale, namespace: 'Puzzle' });

  return (
    <PuzzleEditor />
  );
}
