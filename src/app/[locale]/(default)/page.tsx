import Branding from '@/components/blocks/branding'
import CTA from '@/components/blocks/cta'
import FAQ from '@/components/blocks/faq'
import Feature from '@/components/blocks/feature'
import Feature1 from '@/components/blocks/feature1'
import Hero from '@/components/blocks/hero'
import Pricing from '@/components/blocks/pricing'
import Stats from '@/components/blocks/stats'
import { getLandingPage } from '@/services/page'
import { type Metadata } from 'next'

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`

  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`
  }

  return {
    alternates: {
      canonical: canonicalUrl
    }
  }
}

export default async function LandingPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const page = await getLandingPage(locale)

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      {page.branding && <Branding section={page.branding} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.feature && <Feature section={page.feature} />}
      {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  )
}
