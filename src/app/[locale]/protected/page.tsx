import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'

export default async function ProtectedPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getClaims()
  if (error || !data?.claims) {
    redirect('/auth/login')
  }

  return (
    <div className='container mx-auto py-8'>
      <Card className='max-w-2xl mx-auto'>
        <CardHeader>
          <CardTitle>受保护的页面</CardTitle>
          <CardDescription>这是一个受保护的页面，只有登录用户才能访问。</CardDescription>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>
            恭喜！你已经成功登录并访问了这个受保护的页面。
            如果你没有登录，你会被自动重定向到登录页面。
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
