import { NextIntlClientProvider, hasLocale } from 'next-intl'
import { notFound } from 'next/navigation'
import { routing } from '@/i18n/routing'
import '@/app/globals.css'
import { getTranslations, setRequestLocale } from 'next-intl/server'
import { type Metadata } from 'next'
import { ThemeProvider } from '@/components/theme/theme-provider'
import { Inter } from 'next/font/google'

const fontSans = Inter({
  subsets: ['latin'],
  variable: '--font-sans' // tailwind 有一个内置值默认样式 font-sans，第一个默认使用的是--font-sans，所以这里需要设置则可以用这个字体
})

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations({ locale })

  return {
    title: {
      template: `%s | ${t('metadata.title')}`,
      default: t('metadata.title') || ''
    },
    description: t('metadata.description') || '',
    // keywords: t('metadata.keywords') || '',
    // applicationName: t('metadata.applicationName') || '',
    // authors: [{ name: 'Lenskiss', url: 'https://lenskiss.com' }],
    // generator: 'Next.js',
    // referrer: 'origin-when-cross-origin',
    // creator: 'Lenskiss',
    // publisher: 'Lenskiss',
    // metadataBase: new URL('https://lenskiss.com'),
    // openGraph: {
    //   title: t('metadata.title') || '',
    //   description: t('metadata.description') || '',
    //   images: ['/logo.png']
    // }
  }
}

export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }))
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params
  if (!hasLocale(routing.locales, locale)) {
    notFound()
  }

  // Enable static rendering
  setRequestLocale(locale)

  // Load messages for client components
  const messages = (await import(`@/messages/${locale}.json`)).default

  return (
    <html lang={locale} className={fontSans.variable} suppressHydrationWarning>
      <body className='font-sans min-h-screen antialiased'>
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider attribute='class' disableTransitionOnChange>
            {children}
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
