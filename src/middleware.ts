import createMiddleware from 'next-intl/middleware'
import { type NextRequest } from 'next/server'
import { routing } from './i18n/routing'
import { updateSession } from './lib/supabase/middleware'

const handleI18nRouting = createMiddleware(routing)

export async function middleware(request: NextRequest) {
  const response = handleI18nRouting(request)

  // A `response` can now be passed here
  return await updateSession(request, response)
}

// export default createMiddleware(routing)

export const config = {
  // Match all pathnames except for
  // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
  // - … the ones containing a dot (e.g. `favicon.ico`)

  // 先检查路径不以排除的模式开头（负向前瞻）
  // 然后匹配剩余的所有字符（.*）,也就是文件路由
  matcher: '/((?!api|trpc|_next|_vercel|.*\\..*).*)'
}
