{"metadata": {"title": "几小时内构建任何 AI SaaS 创业项目 | visdraw", "description": "visdraw 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，提供各种模板和组件，帮助您快速启动。", "keywords": "v<PERSON><PERSON><PERSON>, AI SaaS 模板, NextJS 模板"}, "header": {"disabled": false, "brand": {"title": "visdraw", "logo": {"src": "/logo.png", "alt": "visdraw"}, "url": "/zh"}, "nav": {"items": [{"title": "首页", "href": "/zh"}, {"title": "图片工具", "children_column": 2, "children": [{"title": "图片编辑器", "href": "/zh/image-editor", "description": "专业级图片编辑工具，支持滤镜、调色、几何变换等功能。"}, {"title": "拼图编辑器", "href": "/zh/puzzle", "description": "创建精美拼图，支持多种布局模板和图片组合。"}, {"title": "几何变换", "href": "/zh/image-editor/geometry", "description": "专门的几何变换工具，支持旋转、翻转和缩放。"}, {"title": "图片拆分器", "href": "/zh/image-splitter", "description": "将图片拆分成多个小图片，支持自定义行列数和格式。"}]}, {"title": "组件", "children_column": 2, "children": [{"title": "警告对话框", "href": "/zh/docs/primitives/alert-dialog", "description": "一个模态对话框，用于中断用户并显示重要内容，期望用户做出响应。"}, {"title": "悬停卡片", "href": "/zh/docs/primitives/hover-card", "description": "让用户预览链接背后的内容。"}, {"title": "进度条", "href": "/zh/docs/primitives/progress", "description": "显示任务完成进度的指示器，通常以进度条的形式展示。"}, {"title": "滚动区域", "href": "/zh/docs/primitives/scroll-area", "description": "在视觉上或语义上分隔内容。"}, {"title": "标签页", "href": "/zh/docs/primitives/tabs", "description": "一组分层的内容部分——称为标签面板——一次只显示一个。"}, {"title": "工具提示", "href": "/zh/docs/primitives/tooltip", "description": "当元素获得键盘焦点或鼠标悬停在其上时，显示与元素相关信息的弹出框。"}]}, {"title": "文档", "href": "/zh/docs"}, {"title": "列表", "children": [{"title": "组件", "href": "/zh/docs/primitives/alert-dialog", "description": "浏览库中的所有组件。"}, {"title": "文档", "href": "/zh/docs/primitives/alert-dialog", "description": "了解如何使用该库。"}, {"title": "博客", "href": "/zh/docs/primitives/alert-dialog", "description": "阅读我们最新的博客文章。"}]}, {"title": "简单", "children": [{"title": "组件", "href": "/zh/docs/primitives/alert-dialog"}, {"title": "文档", "href": "/zh/docs/primitives/alert-dialog"}, {"title": "区块", "href": "/zh/docs/primitives/alert-dialog"}]}, {"title": "带图标", "show_children_icon": true, "children": [{"title": "待办事项", "href": "/zh/docs/primitives/alert-dialog", "icon": "CircleHelpIcon"}, {"title": "进行中", "href": "/zh/docs/primitives/alert-dialog", "icon": "CircleIcon"}, {"title": "已完成", "href": "/zh/docs/primitives/alert-dialog", "icon": "CircleCheckIcon"}]}]}, "buttons": [{"title": "获取 visdraw", "href": "https://visdraw.com", "target": "_blank", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"disabled": false, "title": "几小时内快速构建 AI 创业项目，而不是几天", "highlight_text": "快速构建", "description": "visdraw 是一个用于构建 AI SaaS 创业项目的 NextJS 模板。<br/>通过丰富的模板和组件快速启动。", "announcement": {"label": "2025", "title": "🎉 新年快乐", "url": "/#pricing"}, "tip": "🎁 2025年前五折优惠", "buttons": [{"title": "立即开始", "icon": "RiFlashlightFill", "url": "/#pricing", "target": "_self", "variant": "default"}, {"title": "加入 Discord", "icon": "RiDiscordFill", "href": "https://discord.gg/HQNnrzjZQS", "target": "_blank", "variant": "outline"}], "show_happy_users": true, "show_badge": false}, "branding": {"title": "visdraw 建立在巨人的肩膀上", "items": [{"title": "Next.js", "image": {"src": "/imgs/logos/nextjs.svg", "alt": "Next.js"}}, {"title": "React", "image": {"src": "/imgs/logos/react.svg", "alt": "React"}}, {"title": "TailwindCSS", "image": {"src": "/imgs/logos/tailwindcss.svg", "alt": "TailwindCSS"}}, {"title": "Shadcn/UI", "image": {"src": "/imgs/logos/shadcn.svg", "alt": "Shadcn/UI"}}, {"title": "Vercel", "image": {"src": "/imgs/logos/vercel.svg", "alt": "Vercel"}}]}, "introduce": {"name": "introduce", "title": "什么是 visdraw", "label": "介绍", "description": "visdraw 是一个用于构建 AI SaaS 创业项目的 NextJS 模板，内置多种模板和组件。", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "即用型模板", "description": "从数十个生产就绪的 AI SaaS 模板中选择，快速启动您的项目。", "icon": "RiNextjsFill"}, {"title": "基础设施配置", "description": "立即获取内置最佳实践的可扩展基础设施。", "icon": "RiDatabase2Line"}, {"title": "快速部署", "description": "在几小时内将您的 AI SaaS 应用部署到生产环境，而不是几天。", "icon": "RiCloudyFill"}]}, "feature": {"name": "feature", "title": "visdraw 核心功能", "description": "快速高效启动 AI SaaS 创业所需的一切。", "items": [{"title": "Next.js 模板", "description": "生产就绪的 Next.js 模板，支持 SEO 友好结构和国际化。", "icon": "RiNextjsFill"}, {"title": "身份验证和支付", "description": "集成 Google OAuth、一键登录和 Stripe 支付处理。", "icon": "RiKey2Fill"}, {"title": "数据基础设施", "description": "内置 Supabase 集成，提供可靠和可扩展的数据存储。", "icon": "RiDatabase2Line"}, {"title": "一键部署", "description": "无缝部署到 Vercel 或 Cloudflare，自动化设置。", "icon": "RiCloudy2Fill"}, {"title": "业务分析", "description": "集成 Google Analytics 和 Search Console 追踪增长。", "icon": "RiBarChart2Line"}, {"title": "AI 就绪基础设施", "description": "预配置 AI 集成，内置积分系统和 API 销售。", "icon": "RiRobot2Line"}]}, "stats": {"name": "stats", "label": "统计", "title": "用户喜爱 visdraw", "description": "因为它易于使用且快速发布。", "icon": "FaRegHeart", "items": [{"title": "信任", "label": "99+", "description": "客户"}, {"title": "内置", "label": "20+", "description": "组件"}, {"title": "快速发布", "label": "5", "description": "分钟"}]}, "pricing": {"name": "pricing", "label": "定价", "title": "定价", "description": "获取 visdraw 的所有功能，快速启动您的 AI SaaS 创业项目。", "groups": [], "items": [{"title": "入门版", "description": "开始您的第一个 SaaS 创业项目。", "features_title": "包含", "features": ["100 积分，有效期 1 个月", "NextJS 模板", "SEO 友好结构", "Stripe 支付", "Supabase 数据存储", "Google OAuth 和一键登录", "国际化支持"], "interval": "one-time", "amount": 9900, "cn_amount": 69900, "currency": "USD", "price": "$99", "original_price": "$199", "unit": "USD", "is_featured": false, "tip": "一次付费，无限项目！", "button": {"title": "获取 visdraw", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "starter", "product_name": "visdraw 模板入门版", "credits": 100, "valid_months": 1}, {"title": "标准版", "description": "快速启动您的 SaaS 创业项目。", "label": "热门", "features_title": "包含入门版所有功能，另加", "features": ["200 积分，有效期 3 个月", "Vercel 或 Cloudflare 部署", "隐私和条款生成", "Google Analytics 集成", "Google Search Console 集成", "Discord 社区", "首次发布技术支持", "终身更新"], "interval": "one-time", "amount": 19900, "cn_amount": 139900, "currency": "USD", "price": "$199", "original_price": "$299", "unit": "USD", "is_featured": true, "tip": "一次付费，无限项目！", "button": {"title": "获取 visdraw", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "standard", "product_name": "visdraw 模板标准版", "credits": 200, "valid_months": 3}, {"title": "高级版", "description": "构建任何 AI SaaS 创业项目。", "features_title": "包含标准版所有功能，另加", "features": ["300 积分，有效期 1 年", "AI 业务功能", "用户中心", "积分系统", "SaaS API 销售", "管理系统", "优先技术支持"], "interval": "one-time", "amount": 29900, "cn_amount": 199900, "currency": "USD", "price": "$299", "original_price": "$399", "unit": "USD", "is_featured": false, "tip": "一次付费，无限项目！", "button": {"title": "获取 visdraw", "url": "/#pricing", "icon": "RiFlashlightFill"}, "product_id": "premium", "product_name": "visdraw 模板高级版", "credits": 300, "valid_months": 12}]}, "faq": {"name": "faq", "label": "常见问题", "title": "关于 visdraw 的常见问题", "description": "还有其他问题？通过 Discord 或电子邮件联系我们。", "items": [{"title": "visdraw 究竟是什么，它是如何工作的？", "description": "visdraw 是一个专门为构建 AI SaaS 创业项目设计的综合性 NextJS 模板。它提供即用型模板、基础设施设置和部署工具，帮助您在几小时内而不是几天内启动 AI 业务。"}, {"title": "使用 visdraw 需要高级技术技能吗？", "description": "虽然基本的编程知识会有帮助，但 visdraw 设计得非常开发者友好。我们的模板和文档使您即使不是 AI 或云基础设施专家也能轻松入门。"}, {"title": "我可以用 visdraw 构建什么类型的 AI SaaS？", "description": "visdraw 支持广泛的 AI 应用，从内容生成到数据分析工具。我们的模板涵盖流行用例，如 AI 聊天机器人、内容生成器、图像处理应用等。"}, {"title": "使用 visdraw 通常需要多长时间才能启动？", "description": "使用 visdraw，您可以在几小时内完成工作原型，并在几小时内完成生产就绪的应用。我们的一键部署和预配置基础设施显著缩短了传统的数月开发周期。"}, {"title": "visdraw 的基础设施包括什么？", "description": "visdraw 提供完整的基础设施栈，包括身份验证、数据库设置、API 集成、支付处理和可扩展的云部署。一切都按照行业最佳实践预先配置。"}, {"title": "我可以自定义模板以匹配我的品牌吗？", "description": "当然可以！所有 visdraw 模板都完全可定制。您可以修改设计、功能和功能性以匹配您的品牌标识和特定业务需求，同时保持强大的底层基础设施。"}]}, "cta": {"name": "cta", "title": "启动您的第一个 AI SaaS 创业项目", "description": "从这里开始，使用 visdraw 启动。", "buttons": [{"title": "获取 visdraw", "url": "https://visdraw.com", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "阅读文档", "url": "https://docs.visdraw.com", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "visdraw", "description": "visdraw 是一个用于构建 AI SaaS 创业项目的 NextJS 模板。通过丰富的模板和组件快速启动。", "logo": {"src": "/logo.png", "alt": "visdraw"}, "url": "/"}, "copyright": "© 2025 • visdraw 保留所有权利。", "nav": {"items": [{"title": "关于", "children": [{"title": "功能特点", "url": "/#feature", "target": "_self"}, {"title": "案例展示", "url": "/#showcase", "target": "_self"}, {"title": "定价", "url": "/#pricing", "target": "_self"}]}, {"title": "资源", "children": [{"title": "文档", "url": "https://docs.visdraw.com", "target": "_blank"}, {"title": "组件", "url": "https://visdraw.com/components", "target": "_blank"}, {"title": "模板", "url": "https://visdraw.com/templates", "target": "_blank"}]}, {"title": "友情链接", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/visdrawai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/visdrawai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "邮箱", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "隐私政策", "url": "/privacy-policy"}, {"title": "服务条款", "url": "/terms-of-service"}]}}, "ImageEditor": {"title": "图片编辑器", "description": "上传图片并使用各种滤镜和调整工具来编辑您的图片", "upload": {"title": "拖拽图片到这里或点击上传", "subtitle": "支持 JPG、PNG、WebP 和 GIF 格式", "supportedFormats": "支持的格式：JPG, PNG, WebP, GIF (最大 50MB)", "changeImage": "更换图片", "tryAgain": "请重试"}, "errors": {"invalidFileType": "不支持的文件格式", "fileTooLarge": "文件大小超过 50MB 限制"}, "controls": {"title": "调整参数", "reset": "重置", "brightness": "亮度", "contrast": "对比度", "saturation": "饱和度", "hue": "色调", "blur": "模糊", "sepia": "怀旧", "grayscale": "灰度"}, "export": {"title": "导出图片", "format": "格式", "quality": "质量", "lowQuality": "低", "highQuality": "高", "download": "下载图片", "pngInfo": "PNG 格式支持透明度，文件较大", "jpegInfo": "JPEG 格式文件较小，但不支持透明度"}}, "Puzzle": {"title": "拼图编辑器", "description": "创建精美拼图，支持多种布局模板和图片组合"}, "ImageSplitter": {"title": "图片拆分器", "description": "将图片拆分成多个小图片，支持自定义行列数和格式"}}