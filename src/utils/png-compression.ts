import UPNG from 'upng-js';

interface CompressOptions {
  /**
   * 压缩质量（[0,1]）
   * @default 0.8
   */
  quality?: number;
  /**
   * 压缩后更大是否使用原图
   * @default true
   */
  noCompressIfLarger?: boolean;
  /**
   * 压缩后的新宽度
   * @default 原尺寸
   */
  width?: number;
  /**
   * 压缩后新高度
   * @default 原尺寸
   */
  height?: number;
}

/**
 * 压缩PNG图片
 * @param file 原始文件
 * @param options 压缩选项
 * @returns 压缩后的文件
 */
export async function compressPNGImage(file: File, options: CompressOptions = {}): Promise<File> {
  const { width, height, quality = 0.8, noCompressIfLarger = true } = options;

  try {
    const arrayBuffer = await file.arrayBuffer();
    const decoded = UPNG.decode(arrayBuffer);
    const rgba8 = UPNG.toRGBA8(decoded);

    const compressed = UPNG.encode(
      rgba8,
      width || decoded.width,
      height || decoded.height,
      Math.floor(256 * quality)
    );

    const newFile = new File([compressed], file.name, { type: 'image/png' });

    if (!noCompressIfLarger) {
      return newFile;
    }

    return file.size > newFile.size ? newFile : file;
  } catch (error) {
    console.error('PNG压缩失败:', error);
    return file; // 压缩失败时返回原文件
  }
}

/**
 * 从Canvas压缩PNG图片
 * @param canvas 画布元素
 * @param options 压缩选项
 * @returns 压缩后的Blob
 */
export async function compressPNGFromCanvas(canvas: HTMLCanvasElement, options: CompressOptions = {}): Promise<Blob> {
  const { quality = 0.8, noCompressIfLarger = true } = options;

  try {
    // 获取原始PNG数据
    const originalBlob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, 'image/png');
    });

    // 将Canvas转换为ImageData
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      return originalBlob;
    }

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const rgba8 = new Uint8Array(imageData.data.buffer);

    // 使用UPNG压缩
    const compressed = UPNG.encode(
      [rgba8.buffer as ArrayBuffer],
      canvas.width,
      canvas.height,
      Math.floor(256 * quality)
    );

    const compressedBlob = new Blob([compressed], { type: 'image/png' });

    // 如果压缩后更大且设置了noCompressIfLarger，返回原始数据
    if (noCompressIfLarger && originalBlob.size <= compressedBlob.size) {
      return originalBlob;
    }

    return compressedBlob;
  } catch (error) {
    console.error('Canvas PNG压缩失败:', error);
    // 压缩失败时返回原始PNG
    return new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, 'image/png');
    });
  }
}

/**
 * 计算压缩比例
 * @param originalSize 原始大小（字节）
 * @param compressedSize 压缩后大小（字节）
 * @returns 压缩比例百分比
 */
export function calculateCompressionRatio(originalSize: number, compressedSize: number): number {
  if (originalSize === 0) return 0;
  return Math.round(((originalSize - compressedSize) / originalSize) * 100);
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
