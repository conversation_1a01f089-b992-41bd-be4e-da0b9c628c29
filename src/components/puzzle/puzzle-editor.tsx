'use client';

import { useState, useRef, useCallback, useEffect, useLayoutEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Download, RotateCcw, Image as ImageIcon, X, Undo2 } from 'lucide-react';
import PuzzleTemplates, { allLayouts } from './puzzle-templates';
import PuzzleCanvas from './puzzle-canvas';
import { PuzzleLayout, PuzzleSlot, PuzzleStyleSettings } from './types';
import SharedSlider from '../image-editor/shared-slider';

// 记录最近选择的布局ID
const STORAGE_KEY = 'puzzle:selectedLayoutId';
// 自定义布局存储键（需与 puzzle-templates.tsx 中一致）
const CUSTOM_LAYOUTS_KEY = 'puzzle:customLayouts';

export default function PuzzleEditor() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const initialLayout: PuzzleLayout | null = allLayouts?.[0] ?? null;
  // 初始使用第一个布局进行 SSR，避免水合不一致；客户端恢复后再显示，杜绝“先第一个再切换”的闪烁
  const [selectedLayout, setSelectedLayout] = useState<PuzzleLayout | null>(initialLayout);
  const [puzzleSlots, setPuzzleSlots] = useState<PuzzleSlot[]>(() =>
    initialLayout
      ? initialLayout.slots.map((slot, index) => ({
          id: `slot-${index}`,
          x: slot.x,
          y: slot.y,
          width: slot.width,
          height: slot.height,
          image: null as HTMLImageElement | null,
          zoom: 1,
          rotation: 0,
          flipH: false,
          flipV: false,
          offsetX: 0,
          offsetY: 0,
        }))
      : []
  );
  const [settings, setSettings] = useState<PuzzleStyleSettings>({
    ratio: { w: 1, h: 1 },
    lockAspectOnResize: false,
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    gap: 0,
    borderRadius: 0,
    cornerRadiusEnabled: false,
    cornerRadius: { tl: 0, tr: 0, br: 0, bl: 0 },
    roundAllSlots: false,
    backgroundColor: '#f8fafc',
    backgroundImage: null,
    moveStep: 0.02,
    zoomStep: 0.1,
    zoomMin: 0.1,
    zoomMax: 5,
    enforceMinZoomBySlotSize: true,
    independentDividerDrag: false,
  });
  // 当前画布像素尺寸（由 PuzzleCanvas 上报）
  const [canvasSize, setCanvasSize] = useState<{ width: number; height: number } | null>(null);
  // 自定义比例输入
  const [customRatio, setCustomRatio] = useState<{ w: string; h: string }>({ w: '1', h: '1' });
  useEffect(() => {
    // 同步当前比例到输入框，便于微调
    setCustomRatio({ w: String(settings.ratio.w), h: String(settings.ratio.h) });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [settings.ratio.w, settings.ratio.h]);

  // 缩放上下限的本地字符串状态，避免输入中间态被强制回退
  const [zoomMinStr, setZoomMinStr] = useState<string>(() => String(settings.zoomMin ?? 0.1));
  const [zoomMaxStr, setZoomMaxStr] = useState<string>(() => String(settings.zoomMax ?? 5));
  useEffect(() => {
    setZoomMinStr(String(settings.zoomMin ?? 0.1));
  }, [settings.zoomMin]);
  useEffect(() => {
    setZoomMaxStr(String(settings.zoomMax ?? 5));
  }, [settings.zoomMax]);

  const commitZoomMin = useCallback(() => {
    const raw = Number(zoomMinStr);
    const currentMax = settings.zoomMax ?? 5;
    const parsed = Number.isFinite(raw) ? raw : (settings.zoomMin ?? 0.1);
    // 下限不小于 0.01，且必须小于 max - 0.01
    const v = Math.max(0.01, Math.min(currentMax - 0.01, parsed));
    setSettings((s) => ({ ...s, zoomMin: v }));
    setZoomMinStr(String(v));
  }, [zoomMinStr, settings.zoomMax, settings.zoomMin]);

  const commitZoomMax = useCallback(() => {
    const raw = Number(zoomMaxStr);
    const currentMin = settings.zoomMin ?? 0.1;
    const parsed = Number.isFinite(raw) ? raw : (settings.zoomMax ?? 5);
    // 上限至少大于 min + 0.01
    const v = Math.max(currentMin + 0.01, parsed);
    setSettings((s) => ({ ...s, zoomMax: v }));
    setZoomMaxStr(String(v));
  }, [zoomMaxStr, settings.zoomMin, settings.zoomMax]);

  const applyCustomRatio = useCallback(() => {
    const rw = parseFloat(customRatio.w);
    const rh = parseFloat(customRatio.h);
    if (!Number.isFinite(rw) || !Number.isFinite(rh)) return;
    const w = Math.max(1, rw);
    const h = Math.max(1, rh);
    setSettings((s) => ({ ...s, ratio: { w, h } }));
  }, [customRatio, setSettings]);
  // 挂载后在客户端恢复上次布局，但在显示前保持整体透明，避免可见闪烁
  const [ready, setReady] = useState(false);
  const isBrowser = typeof window !== 'undefined';
  const useIsoLayoutEffect = (isBrowser ? useLayoutEffect : useEffect) as typeof useEffect;
  useIsoLayoutEffect(() => {
    try {
      if (!isBrowser) return;
      const savedId = localStorage.getItem(STORAGE_KEY);
      if (!savedId) return setReady(true);
      // 在预置模板中查找
      let saved = allLayouts.find((l) => l.id === savedId);
      // 若未找到，尝试在自定义模板中查找
      if (!saved) {
        try {
          const raw = localStorage.getItem(CUSTOM_LAYOUTS_KEY);
          if (raw) {
            const parsed = JSON.parse(raw);
            if (Array.isArray(parsed)) {
              saved = parsed.find((l: any) => l && l.id === savedId) || null;
            }
          }
        } catch {}
      }
      if (!saved) return setReady(true);
      if (saved.id === selectedLayout?.id) return setReady(true);
      setSelectedLayout(saved);
      setPuzzleSlots((prev) => {
        const baseSlots: PuzzleSlot[] = saved.slots.map((slot, index) => ({
          id: `slot-${index}`,
          x: slot.x,
          y: slot.y,
          width: slot.width,
          height: slot.height,
          image: null as HTMLImageElement | null,
          zoom: 1,
          rotation: 0,
          flipH: false,
          flipV: false,
          offsetX: 0,
          offsetY: 0,
        }));

        if (!prev || prev.length === 0) return baseSlots;

        const prevOrdered = [...prev].sort((a, b) => (a.y - b.y) || (a.x - b.x));
        const images: HTMLImageElement[] = prevOrdered
          .map((s) => s.image)
          .filter((img): img is HTMLImageElement => Boolean(img));

        if (images.length === 0) return baseSlots;

        const newOrderIdx = baseSlots
          .map((s, idx) => ({ idx, x: s.x, y: s.y }))
          .sort((A, B) => (A.y - B.y) || (A.x - B.x))
          .map((it) => it.idx);

        const assigned = [...baseSlots];
        const count = Math.min(images.length, assigned.length);
        for (let i = 0; i < count; i++) {
          assigned[newOrderIdx[i]].image = images[i];
        }
        return assigned;
      });
    } catch {}
    setReady(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 保存当前布局为模板（仅保存几何，不保存图片等运行时状态）
  const handleSaveCurrentLayout = useCallback(() => {
    try {
      const slots = puzzleSlots.map((s) => ({ x: s.x, y: s.y, width: s.width, height: s.height }));
      if (slots.length === 0) {
        alert('当前没有可保存的槽位。');
        return;
      }
      const id = `custom-${Date.now()}`;
      const name = `自定义 当前布局（${slots.length}块）`;
      const layout: PuzzleLayout = { id, name, imageCount: slots.length, slots };

      const raw = typeof window !== 'undefined' ? localStorage.getItem(CUSTOM_LAYOUTS_KEY) : null;
      let list: PuzzleLayout[] = [];
      if (raw) {
        try { const parsed = JSON.parse(raw); if (Array.isArray(parsed)) list = parsed; } catch {}
      }
      const next = [...list, layout];
      if (typeof window !== 'undefined') {
        try { localStorage.setItem(CUSTOM_LAYOUTS_KEY, JSON.stringify(next)); } catch {}
        // 通知模板面板刷新
        try { window.dispatchEvent(new Event('puzzle:customLayouts:updated')); } catch {}
      }
      // 可选：不强制切换，避免重排导致图片按阅读顺序重新分配；若想立即使用，可取消注释：
      // handleLayoutSelect(layout);
      alert('已保存为自定义布局。可在左侧“我的布局”中使用。');
    } catch (e) {
      console.error(e);
      alert('保存失败，请重试。');
    }
  }, [puzzleSlots]);

  const handleLayoutSelect = useCallback((layout: PuzzleLayout) => {
    // 持久化最近选择
    if (typeof window !== 'undefined') {
      try { localStorage.setItem(STORAGE_KEY, layout.id); } catch {}
    }
    setSelectedLayout(layout);
    // 切换布局时清空撤回历史
    setHistory([]);
    // 根据新布局生成基础槽位，然后将旧布局中的图片按阅读顺序(上->下，左->右)映射到新槽位
    setPuzzleSlots((prev) => {
      const baseSlots: PuzzleSlot[] = layout.slots.map((slot, index) => ({
        id: `slot-${index}`,
        x: slot.x,
        y: slot.y,
        width: slot.width,
        height: slot.height,
        image: null as HTMLImageElement | null,
        zoom: 1,
        rotation: 0,
        flipH: false,
        flipV: false,
        offsetX: 0,
        offsetY: 0,
      }));

      if (!prev || prev.length === 0) return baseSlots;

      // 取出旧槽位中的图片（阅读顺序）
      const prevOrdered = [...prev].sort((a, b) => (a.y - b.y) || (a.x - b.x));
      const images: HTMLImageElement[] = prevOrdered
        .map((s) => s.image)
        .filter((img): img is HTMLImageElement => Boolean(img));

      if (images.length === 0) return baseSlots;

      // 将图片按阅读顺序填充到新的槽位
      const newOrderIdx = baseSlots
        .map((s, idx) => ({ idx, x: s.x, y: s.y }))
        .sort((A, B) => (A.y - B.y) || (A.x - B.x))
        .map((it) => it.idx);

      const assigned = [...baseSlots];
      const count = Math.min(images.length, assigned.length);
      for (let i = 0; i < count; i++) {
        assigned[newOrderIdx[i]].image = images[i];
      }

      return assigned;
    });
  }, []);

  const handleImageUpload = useCallback((slotId: string, file: File) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      setPuzzleSlots(prev => prev.map(slot => 
        slot.id === slotId 
          ? { ...slot, image: img }
          : slot
      ));
      URL.revokeObjectURL(url);
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      alert('图片加载失败，请检查文件是否损坏。');
    };

    img.src = url;
  }, []);

  const handleReset = useCallback(() => {
    // 使用已保存的布局（若无则退回到第一个布局），并按阅读顺序映射图片
    const savedId = typeof window !== 'undefined' ? localStorage.getItem(STORAGE_KEY) : null;
    // 先在预置模板中找，再在自定义模板中找
    let layout: PuzzleLayout | null = savedId ? (allLayouts.find(l => l.id === savedId) ?? null) : null;
    if (!layout && typeof window !== 'undefined') {
      try {
        const raw = localStorage.getItem(CUSTOM_LAYOUTS_KEY);
        if (raw) {
          const parsed = JSON.parse(raw);
          if (Array.isArray(parsed)) {
            const found = parsed.find((l: any) => l && l.id === savedId);
            if (found) layout = found as PuzzleLayout;
          }
        }
      } catch {}
    }
    layout = layout ?? initialLayout;
    if (layout) {
      // 确保持久化为该布局
      if (typeof window !== 'undefined') {
        try { localStorage.setItem(STORAGE_KEY, layout.id); } catch {}
      }
      setSelectedLayout(layout);
      // 重置时清空撤回历史
      setHistory([]);
      setPuzzleSlots((prev) => {
        const baseSlots: PuzzleSlot[] = layout.slots.map((slot, index) => ({
          id: `slot-${index}`,
          x: slot.x,
          y: slot.y,
          width: slot.width,
          height: slot.height,
          image: null as HTMLImageElement | null,
          zoom: 1,
          rotation: 0,
          flipH: false,
          flipV: false,
          offsetX: 0,
          offsetY: 0,
        }));

        if (!prev || prev.length === 0) return baseSlots;

        // 取出旧槽位中的图片（阅读顺序）
        const prevOrdered = [...prev].sort((a, b) => (a.y - b.y) || (a.x - b.x));
        const images: HTMLImageElement[] = prevOrdered
          .map((s) => s.image)
          .filter((img): img is HTMLImageElement => Boolean(img));

        if (images.length === 0) return baseSlots;

        // 将图片按阅读顺序填充到新的槽位
        const newOrderIdx = baseSlots
          .map((s, idx) => ({ idx, x: s.x, y: s.y }))
          .sort((A, B) => (A.y - B.y) || (A.x - B.x))
          .map((it) => it.idx);

        const assigned = [...baseSlots];
        const count = Math.min(images.length, assigned.length);
        for (let i = 0; i < count; i++) {
          assigned[newOrderIdx[i]].image = images[i];
        }
        return assigned;
      });
    } else {
      // 若无初始布局，回退到清空
      setSelectedLayout(null);
      setPuzzleSlots([]);
    }

    // 重置样式设置
    setSettings({
      ratio: { w: 1, h: 1 },
      lockAspectOnResize: false,
      padding: { top: 0, right: 0, bottom: 0, left: 0 },
      gap: 0,
      borderRadius: 0,
      cornerRadiusEnabled: false,
      cornerRadius: { tl: 0, tr: 0, br: 0, bl: 0 },
      roundAllSlots: true,
      backgroundColor: '#f8fafc',
      backgroundImage: null,
      moveStep: 0.02,
      zoomStep: 0.1,
      zoomMin: 0.1,
      zoomMax: 5,
      enforceMinZoomBySlotSize: false,
    });
  }, [initialLayout]);

  const handleBgImageUpload = useCallback((file: File) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    img.onload = () => {
      setSettings((prev) => ({ ...prev, backgroundImage: img }));
      URL.revokeObjectURL(url);
    };
    img.onerror = () => {
      URL.revokeObjectURL(url);
      alert('背景图加载失败');
    };
    img.src = url;
  }, []);

  const handleDownload = useCallback(async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    try {
      canvas.toBlob((blob) => {
        if (!blob) return;
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.download = 'puzzle.png';
        link.href = url;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
      }, 'image/png');
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败，请重试。');
    }
  }, []);

  // 显示用比例格式化：优先化简为整数比，否则保留两位小数
  const formatRatio = useCallback((w: number, h: number) => {
    const nearlyInt = (n: number) => Math.abs(n - Math.round(n)) < 0.02;
    const trim = (s: string) => s.replace(/\.?0+$/, '');
    if (Number.isFinite(w) && Number.isFinite(h) && w > 0 && h > 0) {
      if (nearlyInt(w) && nearlyInt(h)) {
        let ai = Math.max(1, Math.round(w));
        let bi = Math.max(1, Math.round(h));
        const gcd = (x: number, y: number): number => (y === 0 ? x : gcd(y, x % y));
        const g = Math.max(1, gcd(ai, bi));
        ai = Math.max(1, Math.floor(ai / g));
        bi = Math.max(1, Math.floor(bi / g));
        return `${ai}:${bi}`;
      }
      return `${trim(w.toFixed(2))}:${trim(h.toFixed(2))}`;
    }
    return '—';
  }, []);

  // 选择/合并模式
  const [selectionEnabled, setSelectionEnabled] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  // 撤回历史，仅记录结构合并前的 slots
  const [history, setHistory] = useState<PuzzleSlot[][]>([]);

  const handleUndo = useCallback(() => {
    setHistory((h) => {
      if (!h || h.length === 0) return h;
      const next = [...h];
      const last = next.pop();
      if (last) setPuzzleSlots(last);
      return next;
    });
  }, []);

  // 快捷键：在合并模式下支持 Cmd/Ctrl+Z 撤回
  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (!selectionEnabled) return;
      const isUndo = (e.metaKey || e.ctrlKey) && !e.shiftKey && (e.key === 'z' || e.key === 'Z');
      if (isUndo) {
        e.preventDefault();
        handleUndo();
      }
    };
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectionEnabled, history]);

  const toggleSelect = useCallback((slotId: string) => {
    setSelectedIds((prev) => {
      if (prev.includes(slotId)) return prev.filter((id) => id !== slotId);
      return [...prev, slotId];
    });
  }, []);

  const clearSelection = useCallback(() => setSelectedIds([]), []);

  const rectOverlap = (a: {x:number;y:number;width:number;height:number}, b: {x:number;y:number;width:number;height:number}) => {
    return !(a.x + a.width <= b.x || b.x + b.width <= a.x || a.y + a.height <= b.y || b.y + b.height <= a.y);
  };

  const handleMergeSelected = useCallback(() => {
    if (selectedIds.length < 2) {
      alert('请至少选择两个区块进行合并');
      return;
    }
    // 取出被选槽位
    const selected = puzzleSlots.filter((s) => selectedIds.includes(s.id));
    if (selected.length < 2) return;
    // 计算包围盒
    const minX = Math.min(...selected.map((s) => s.x));
    const minY = Math.min(...selected.map((s) => s.y));
    const maxX = Math.max(...selected.map((s) => s.x + s.width));
    const maxY = Math.max(...selected.map((s) => s.y + s.height));
    const bbox = { x: minX, y: minY, width: Math.max(0, maxX - minX), height: Math.max(0, maxY - minY) };
    // 安全性：不允许有未选中的槽与包围盒相交，否则可能产生重叠
    const others = puzzleSlots.filter((s) => !selectedIds.includes(s.id));
    const unsafe = others.some((s) => rectOverlap({ x: s.x, y: s.y, width: s.width, height: s.height }, bbox));
    if (unsafe) {
      alert('当前选择范围与其他未选区块有重叠，无法安全合并。请调整选择。');
      return;
    }
    // 新槽位：保留阅读序首个图片，其余丢弃（可后续扩展为图片拼接）
    const readingOrder = [...selected].sort((a, b) => (a.y - b.y) || (a.x - b.x));
    const first = readingOrder[0];
    const merged: PuzzleSlot = {
      id: `slot-${Date.now()}`,
      x: bbox.x,
      y: bbox.y,
      width: bbox.width,
      height: bbox.height,
      image: first?.image ?? null,
      zoom: 1,
      rotation: 0,
      flipH: false,
      flipV: false,
      offsetX: 0,
      offsetY: 0,
    };
    // 删除已选槽位并加入新槽位（入栈撤回历史）
    // 历史限制最多 20 步；使用浅拷贝快照，保持 image 引用
    setHistory((h) => {
      const snapshot = puzzleSlots.map((s) => ({ ...s }));
      const next = [...h, snapshot];
      return next.length > 20 ? next.slice(next.length - 20) : next;
    });
    setPuzzleSlots((prev) => {
      const remain = prev.filter((s) => !selectedIds.includes(s.id));
      return [...remain, merged];
    });
    // 清理选择
    setSelectedIds([]);
    setSelectionEnabled(false);
  }, [puzzleSlots, selectedIds]);

  

  return (
    <div className={`h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50 dark:from-slate-900 dark:via-slate-900 dark:to-indigo-950/50 flex flex-col relative overflow-hidden transition-opacity ${ready ? 'opacity-100' : 'opacity-0'}`}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-blue-400/15 to-purple-600/15 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '4s' }}></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-tr from-indigo-400/15 to-pink-600/15 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '5s', animationDelay: '1s' }}></div>
      </div>

      

      {/* 主要内容区域 */}
      <div className="flex-1 flex min-h-0 relative">
        {/* 模板选择侧边栏 */}
        <div className="flex-shrink-0 w-80 h-full flex flex-col min-h-0 border-r border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-b from-white/95 via-white/90 to-slate-50/95 dark:from-slate-900/95 dark:via-slate-900/90 dark:to-slate-800/95 backdrop-blur-sm relative">
          <div className="absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-blue-500 via-purple-500 to-indigo-500 opacity-60"></div>
          <PuzzleTemplates
            onLayoutSelect={handleLayoutSelect}
            selectedLayout={selectedLayout}
            className="h-full"
          />
        </div>

        {/* 画布区域 */}
        <div className="flex-1 flex flex-col min-w-0">
          <div className="flex-1 overflow-auto p-8 relative">
            {/* 顶部比例/尺寸显示 */}
            <div className="absolute top-0 left-0 z-10">
              <div className="inline-flex items-center gap-3 rounded-md bg-white/70 dark:bg-slate-900/60 backdrop-blur px-2.5 py-1 border border-slate-200/60 dark:border-slate-700/60 shadow-sm text-xs">
                <div className="inline-flex items-center gap-1.5">
                  <span className="text-slate-500">宽高比</span>
                  <span className="font-medium text-slate-900 dark:text-slate-100">{formatRatio(settings.ratio.w, settings.ratio.h)}</span>
                </div>
                <div className="h-3 w-px bg-slate-200 dark:bg-slate-700" />
                <div className="inline-flex items-center gap-1.5">
                  <span className="text-slate-500">尺寸</span>
                  <span className="font-medium text-slate-900 dark:text-slate-100">{canvasSize ? `${canvasSize.width}×${canvasSize.height}px` : '—'}</span>
                </div>
              </div>
            </div>
            <div className="min-h-full flex items-center justify-center">
              <PuzzleCanvas
                ref={canvasRef}
                layout={selectedLayout}
                slots={puzzleSlots}
                onImageUpload={handleImageUpload}
                onSlotsChange={(slots) => setPuzzleSlots(slots)}
                className="max-w-none shadow-2xl overflow-hidden"
                settings={settings}
                selectionEnabled={selectionEnabled}
                selectedSlotIds={selectedIds}
                onSlotSelectToggle={toggleSelect}
                onClearSelection={clearSelection}
                onRatioChange={(ratio) =>
                  setSettings((s) => ({
                    ...s,
                    ratio: {
                      w: Math.max(1, Number.isFinite(ratio.w) ? ratio.w : 1),
                      h: Math.max(1, Number.isFinite(ratio.h) ? ratio.h : 1),
                    },
                  }))
                }
                onCanvasSizeChange={setCanvasSize}
              />
            </div>
          </div>
        </div>

        {/* 右侧设置面板 */}
        <div className="flex-shrink-0 w-80 h-full flex flex-col min-h-0 border-l border-slate-200/60 dark:border-slate-800/60 bg-gradient-to-b from-white/85 via-white/75 to-white/65 dark:from-slate-900/85 dark:via-slate-900/75 dark:to-slate-900/65 backdrop-blur-md">
          {/* 面板标题 + 快速操作（吸顶）*/}
          <div className="sticky top-0 z-20 px-3 py-2 border-b border-slate-200/60 dark:border-slate-800/60 bg-white/80 dark:bg-slate-900/80 backdrop-blur-md">
            <div className="text-[11px] font-semibold tracking-wide text-slate-700 dark:text-slate-200">拼图设置</div>
            {/* 快速操作区：将常用功能上移到顶部，便于优先访问 */}
            <div className="mt-2 flex flex-wrap items-center gap-2">
              <Button
                onClick={handleDownload}
                disabled={!selectedLayout || puzzleSlots.every(slot => !slot.image)}
                className="h-7 px-2 bg-blue-600 hover:bg-blue-700 text-white text-xs"
              >
                <Download className="w-3.5 h-3.5 mr-1.5" />
                下载拼图
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="h-7 px-2 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 text-xs"
              >
                <RotateCcw className="w-3.5 h-3.5 mr-1.5" />
                重置
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveCurrentLayout}
                className="h-7 px-2 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 text-xs"
              >
                保存当前布局
              </Button>
              <div className="h-4 w-px bg-slate-200 dark:bg-slate-800" />
              <Button
                variant={selectionEnabled ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  const next = !selectionEnabled;
                  setSelectionEnabled(next);
                  if (!next) setSelectedIds([]);
                }}
                className="h-7 px-2 text-xs"
              >
                {selectionEnabled ? '合并模式: 开' : '合并模式: 关'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleUndo}
                disabled={history.length === 0}
                className="h-7 px-2 text-xs"
                title={history.length === 0 ? '没有可撤回的操作' : '撤回上一步合并'}
              >
                <Undo2 className="w-3.5 h-3.5 mr-1.5" /> 撤回
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={!selectionEnabled || selectedIds.length < 2}
                onClick={handleMergeSelected}
                className="h-7 px-2 text-xs"
                title="至少选择两个区块"
              >
                合并所选
              </Button>
              {selectionEnabled && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSelection}
                  className="h-7 px-2 text-xs"
                >
                  清空选择
                </Button>
              )}
            </div>
          </div>
          {/* 内容滚动区 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-5">
            <div className="border-t border-slate-200/80 dark:border-slate-800/80 pt-4 space-y-3">
              <h3 className="text-[13px] font-medium text-slate-800 dark:text-slate-100 mb-2">画布比例</h3>
              <div className="flex flex-wrap gap-2">
                {[
                  { label: '1:1', w: 1, h: 1 },
                  { label: '3:4', w: 3, h: 4 },
                  { label: '4:3', w: 4, h: 3 },
                  { label: '9:16', w: 9, h: 16 },
                  { label: '16:9', w: 16, h: 9 },
                ].map((r) => (
                  <Button
                    key={r.label}
                    variant={settings.ratio.w === r.w && settings.ratio.h === r.h ? 'default' : 'outline'}
                    size="sm"
                    className="h-7 px-2 text-xs"
                    onClick={() => setSettings((s) => ({ ...s, ratio: { w: r.w, h: r.h } }))}
                  >
                    {r.label}
                  </Button>
                ))}
              </div>
              {/* 自定义比例 */}
              <div className="mt-2 grid grid-cols-3 gap-2 items-end">
                <div>
                  <Label htmlFor="ratio-w" className="mb-1 block">宽</Label>
                  <Input
                    id="ratio-w"
                    type="number"
                    min={1}
                    step={0.01}
                    value={customRatio.w}
                    onChange={(e) => setCustomRatio((c) => ({ ...c, w: e.target.value }))}
                    onKeyDown={(e) => { if (e.key === 'Enter') applyCustomRatio(); }}
                    className="h-9 px-2 text-xs"
                  />
                </div>
                <div>
                  <Label htmlFor="ratio-h" className="mb-1 block">高</Label>
                  <Input
                    id="ratio-h"
                    type="number"
                    min={1}
                    step={0.01}
                    value={customRatio.h}
                    onChange={(e) => setCustomRatio((c) => ({ ...c, h: e.target.value }))}
                    onKeyDown={(e) => { if (e.key === 'Enter') applyCustomRatio(); }}
                    className="h-9 px-2 text-xs"
                  />
                </div>
                <div className="flex items-end">
                  <Button className="h-8 w-full" variant="outline" size="sm" onClick={applyCustomRatio}>设置</Button>
                </div>
              </div>
              <div className="mt-2 flex items-center justify-between">
                <div className="text-xs text-slate-700 dark:text-slate-200">锁定比例拖拽</div>
                <Switch
                  checked={Boolean(settings.lockAspectOnResize)}
                  onCheckedChange={(v) => setSettings((s) => ({ ...s, lockAspectOnResize: Boolean(v) }))}
                />
              </div>
              <div className="mt-2 flex items-center justify-between">
                <div className="text-xs text-slate-700 dark:text-slate-200">分割线独立拖拽</div>
                <Switch
                  checked={Boolean(settings.independentDividerDrag)}
                  onCheckedChange={(v) => setSettings((s) => ({ ...s, independentDividerDrag: Boolean(v) }))}
                />
              </div>
            </div>
            <div className="border-t border-slate-200/80 dark:border-slate-800/80 pt-4 space-y-3">
              <h3 className="text-[13px] font-medium text-slate-800 dark:text-slate-100 mb-2">边距与间距（px）</h3>
              <div className="grid grid-cols-2 gap-1.5">
                <div className="space-y-1">
                  <SharedSlider
                    label="上"
                    value={settings.padding.top}
                    min={0}
                    max={200}
                    step={1}
                    unit="px"
                    onChange={(value) => setSettings((s) => ({ ...s, padding: { ...s.padding, top: Math.max(0, value) } }))}
                    size="sm"
                  />
                  <Input
                    aria-label="上边距输入"
                    type="number"
                    min={0}
                    max={200}
                    step={1}
                    value={settings.padding.top}
                    onChange={(e) => setSettings((s) => ({ ...s, padding: { ...s.padding, top: Math.max(0, Number(e.target.value) || 0) } }))}
                    className="h-8 px-2 text-xs w-full"
                  />
                </div>
                <div className="space-y-1">
                  <SharedSlider
                    label="右"
                    value={settings.padding.right}
                    min={0}
                    max={200}
                    step={1}
                    unit="px"
                    onChange={(value) => setSettings((s) => ({ ...s, padding: { ...s.padding, right: Math.max(0, value) } }))}
                    size="sm"
                  />
                  <Input
                    aria-label="右边距输入"
                    type="number"
                    min={0}
                    max={200}
                    step={1}
                    value={settings.padding.right}
                    onChange={(e) => setSettings((s) => ({ ...s, padding: { ...s.padding, right: Math.max(0, Number(e.target.value) || 0) } }))}
                    className="h-8 px-2 text-xs w-full"
                  />
                </div>
                <div className="space-y-1">
                  <SharedSlider
                    label="下"
                    value={settings.padding.bottom}
                    min={0}
                    max={200}
                    step={1}
                    unit="px"
                    onChange={(value) => setSettings((s) => ({ ...s, padding: { ...s.padding, bottom: Math.max(0, value) } }))}
                    size="sm"
                  />
                  <Input
                    aria-label="下边距输入"
                    type="number"
                    min={0}
                    max={200}
                    step={1}
                    value={settings.padding.bottom}
                    onChange={(e) => setSettings((s) => ({ ...s, padding: { ...s.padding, bottom: Math.max(0, Number(e.target.value) || 0) } }))}
                    className="h-8 px-2 text-xs w-full"
                  />
                </div>
                <div className="space-y-1">
                  <SharedSlider
                    label="左"
                    value={settings.padding.left}
                    min={0}
                    max={200}
                    step={1}
                    unit="px"
                    onChange={(value) => setSettings((s) => ({ ...s, padding: { ...s.padding, left: Math.max(0, value) } }))}
                    size="sm"
                  />
                  <Input
                    aria-label="左边距输入"
                    type="number"
                    min={0}
                    max={200}
                    step={1}
                    value={settings.padding.left}
                    onChange={(e) => setSettings((s) => ({ ...s, padding: { ...s.padding, left: Math.max(0, Number(e.target.value) || 0) } }))}
                    className="h-8 px-2 text-xs w-full"
                  />
                </div>
              </div>
              <SharedSlider
                label="槽间距"
                value={settings.gap}
                min={0}
                max={40}
                step={1}
                unit="px"
                onChange={(value) => setSettings((s) => ({ ...s, gap: Math.max(0, value) }))}
                size="sm"
              />
              <div className="flex items-center gap-2">
                <Label htmlFor="gap-input" className="text-xs text-slate-600 dark:text-slate-300">输入</Label>
                <Input
                  id="gap-input"
                  type="number"
                  min={0}
                  max={40}
                  step={1}
                  value={settings.gap}
                  onChange={(e) => setSettings((s) => ({ ...s, gap: Math.max(0, Number(e.target.value) || 0) }))}
                  className="h-8 px-2 text-xs w-24"
                />
              </div>
            </div>
            

            {/* 圆角设置 */}
            <div className="border-t border-slate-200/80 dark:border-slate-800/80 pt-4 space-y-3">
              <h3 className="text-[13px] font-medium text-slate-800 dark:text-slate-100">圆角设置</h3>
              <div className="flex items-center justify-between">
                <div className="text-xs text-slate-700 dark:text-slate-200">每块圆角</div>
                <Switch
                  checked={Boolean(settings.roundAllSlots)}
                  onCheckedChange={(v) => setSettings((s) => ({ ...s, roundAllSlots: Boolean(v) }))}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="text-xs text-slate-700 dark:text-slate-200">独立四角圆角</div>
                <Switch
                  checked={Boolean(settings.cornerRadiusEnabled)}
                  onCheckedChange={(v) => setSettings((s) => {
                    const enable = Boolean(v);
                    if (enable) {
                      // 打开时，将当前统一圆角同步到四个角，便于继续微调
                      const br = Math.max(0, s.borderRadius || 0);
                      return { ...s, cornerRadiusEnabled: true, cornerRadius: { tl: br, tr: br, br: br, bl: br } };
                    }
                    return { ...s, cornerRadiusEnabled: false };
                  })}
                />
              </div>

              {!settings.cornerRadiusEnabled && (
                <SharedSlider
                  label="圆角"
                  value={settings.borderRadius}
                  min={0}
                  max={64}
                  step={1}
                  unit="px"
                  onChange={(value) => setSettings((s) => ({ ...s, borderRadius: Math.max(0, value) }))}
                  size="sm"
                />
              )}

              {settings.cornerRadiusEnabled && (
                <div className="grid grid-cols-2 gap-1.5">
                  <SharedSlider
                    label="左上"
                    value={settings.cornerRadius?.tl ?? 0}
                    min={0}
                    max={64}
                    step={1}
                    unit="px"
                    onChange={(value) => setSettings((s) => ({ ...s, cornerRadius: { ...(s.cornerRadius ?? { tl: 0, tr: 0, br: 0, bl: 0 }), tl: Math.max(0, value) } }))}
                    size="sm"
                  />
                  <SharedSlider
                    label="右上"
                    value={settings.cornerRadius?.tr ?? 0}
                    min={0}
                    max={64}
                    step={1}
                    unit="px"
                    onChange={(value) => setSettings((s) => ({ ...s, cornerRadius: { ...(s.cornerRadius ?? { tl: 0, tr: 0, br: 0, bl: 0 }), tr: Math.max(0, value) } }))}
                    size="sm"
                  />
                  <SharedSlider
                    label="右下"
                    value={settings.cornerRadius?.br ?? 0}
                    min={0}
                    max={64}
                    step={1}
                    unit="px"
                    onChange={(value) => setSettings((s) => ({ ...s, cornerRadius: { ...(s.cornerRadius ?? { tl: 0, tr: 0, br: 0, bl: 0 }), br: Math.max(0, value) } }))}
                    size="sm"
                  />
                  <SharedSlider
                    label="左下"
                    value={settings.cornerRadius?.bl ?? 0}
                    min={0}
                    max={64}
                    step={1}
                    unit="px"
                    onChange={(value) => setSettings((s) => ({ ...s, cornerRadius: { ...(s.cornerRadius ?? { tl: 0, tr: 0, br: 0, bl: 0 }), bl: Math.max(0, value) } }))}
                    size="sm"
                  />
                </div>
              )}
            </div>
            {/* 微调移动配置 */}
            <div className="border-t border-slate-200/80 dark:border-slate-800/80 pt-4 space-y-2">
              <h3 className="text-[13px] font-medium text-slate-800 dark:text-slate-100">微调移动</h3>
              <SharedSlider
                label="每次移动"
                value={Math.round(((settings.moveStep ?? 0.02) * 100 + Number.EPSILON) * 10) / 10}
                min={0.5}
                max={10}
                step={0.5}
                unit="%"
                onChange={(value) => setSettings((s) => ({ ...s, moveStep: Math.max(0.0001, (value || 0.5) / 100) }))}
                size="sm"
              />
            </div>
            {/* 缩放控制 */}
            <div className="border-t border-slate-200/80 dark:border-slate-800/80 pt-4 space-y-3">
              <h3 className="text-[13px] font-medium text-slate-800 dark:text-slate-100">缩放控制</h3>
              <div className="space-y-2">
                <div className="text-[11px] font-medium text-slate-500 dark:text-slate-400">步长设置</div>
                <SharedSlider
                  label="每次缩放"
                  value={Math.round(((settings.zoomStep ?? 0.1) + Number.EPSILON) * 100) / 100}
                  min={0.01}
                  max={1}
                  step={0.01}
                  onChange={(value) => setSettings((s) => ({ ...s, zoomStep: Math.min(1, Math.max(0.01, value)) }))}
                  size="sm"
                />
              </div>
              <div className="space-y-2">
                <div className="text-[11px] font-medium text-slate-500 dark:text-slate-400">限制设置</div>
                <div className="grid grid-cols-2 gap-1.5">
                  <div>
                    <Label htmlFor="zoom-min" className="mb-1 block">最小缩放</Label>
                    <Input
                      id="zoom-min"
                      type="text"
                      inputMode="decimal"
                      step={0.05}
                      value={zoomMinStr}
                      onChange={(e) => setZoomMinStr(e.target.value)}
                      onBlur={commitZoomMin}
                      onKeyDown={(e) => { if (e.key === 'Enter') commitZoomMin(); }}
                      disabled={Boolean(settings.enforceMinZoomBySlotSize)}
                      className="h-9 px-2 text-xs"
                    />
                  </div>
                  <div>
                    <Label htmlFor="zoom-max" className="mb-1 block">最大缩放</Label>
                    <Input
                      id="zoom-max"
                      type="text"
                      inputMode="decimal"
                      step={0.05}
                      value={zoomMaxStr}
                      onChange={(e) => setZoomMaxStr(e.target.value)}
                      onBlur={commitZoomMax}
                      onKeyDown={(e) => { if (e.key === 'Enter') commitZoomMax(); }}
                      disabled={Boolean(settings.enforceMinZoomBySlotSize)}
                      className="h-9 px-2 text-xs"
                    />
                  </div>
                </div>
                <div className="mt-1.5 flex items-center justify-between">
                  <div className="text-xs text-slate-700 dark:text-slate-200">按槽位限制最小缩放</div>
                  <Switch
                    checked={Boolean(settings.enforceMinZoomBySlotSize)}
                    onCheckedChange={(v) => setSettings((s) => ({ ...s, enforceMinZoomBySlotSize: Boolean(v) }))}
                  />
                </div>
              </div>
            </div>
            {/* 画布背景 */}
            <div className="border-t border-slate-200/80 dark:border-slate-800/80 pt-4 space-y-3">
              <h3 className="text-[13px] font-medium text-slate-800 dark:text-slate-100 mb-2">画布背景</h3>
              <div className="space-y-2">
                <div className="text-[11px] font-medium text-slate-500 dark:text-slate-400">背景颜色</div>
                <div className="flex items-center gap-4">
                  <Input type="color" value={settings.backgroundColor}
                    onChange={(e) => setSettings((s) => ({ ...s, backgroundColor: e.target.value }))} className="h-9 p-1.5" />
                  <Input type="text" value={settings.backgroundColor}
                    onChange={(e) => setSettings((s) => ({ ...s, backgroundColor: e.target.value }))}
                    className="h-9 px-2 text-xs" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-[11px] font-medium text-slate-500 dark:text-slate-400">背景图片</div>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = 'image/*';
                      input.onchange = (e) => {
                        const file = (e.target as HTMLInputElement).files?.[0];
                        if (file) handleBgImageUpload(file);
                      };
                      input.click();
                    }}
                  >
                    <ImageIcon className="w-3.5 h-3.5 mr-1.5" />上传
                  </Button>
                  {settings.backgroundImage && (
                    <Button type="button" variant="ghost" size="sm" onClick={() => setSettings((s) => ({ ...s, backgroundImage: null }))}>
                      <X className="w-3.5 h-3.5 mr-1.5" />清除
                    </Button>
                  )}
                </div>
                {settings.backgroundImage && (
                  <div className="mt-1 text-xs text-slate-600 dark:text-slate-300">已设置背景图片</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
