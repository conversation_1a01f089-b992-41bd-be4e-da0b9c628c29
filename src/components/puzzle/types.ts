export interface PuzzleSlotTemplate {
  x: number;      // 相对位置 (0-1)
  y: number;      // 相对位置 (0-1)
  width: number;  // 相对宽度 (0-1)
  height: number; // 相对高度 (0-1)
}

export interface PuzzleLayout {
  id: string;
  name: string;
  imageCount: number;
  slots: PuzzleSlotTemplate[];
  preview?: string; // 预览图片路径
}

export interface PuzzleSlot {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  image: HTMLImageElement | null;
  // per-image transforms
  zoom: number;       // 放大倍数，默认 1
  rotation: number;   // 旋转角度（度），默认 0
  flipH: boolean;     // 水平翻转
  flipV: boolean;     // 垂直翻转
  // 平移（相对槽位尺寸的比例偏移，左负右正，上负下正），默认 0
  offsetX: number;
  offsetY: number;
}

export interface PuzzleStyleSettings {
  // 画布比例：宽:高
  ratio: { w: number; h: number };
  // 是否在拖拽调整画布大小时锁定比例（仅改变像素尺度，不改变 ratio）
  lockAspectOnResize?: boolean;
  // 画布内边距（像素）
  padding: { top: number; right: number; bottom: number; left: number };
  // 槽位之间的间距（像素）
  gap: number;
  // 槽位圆角（像素）
  borderRadius: number;
  // 是否启用独立四角圆角
  cornerRadiusEnabled?: boolean;
  // 四个角的圆角（像素）
  cornerRadius?: { tl: number; tr: number; br: number; bl: number };
  // 是否对每个区块都应用圆角（包括内部分割处）。默认 false 仅外部角圆角。
  roundAllSlots?: boolean;
  // 是否启用内角圆角（仅在 gap>0 时可见）
  innerCornerRadiusEnabled?: boolean;
  // 内角圆角半径（像素）。当未设置或为无效值时，默认回退到 borderRadius
  innerCornerRadius?: number;
  // 背景颜色（CSS color）
  backgroundColor: string;
  // 背景图片（可选）
  backgroundImage: HTMLImageElement | null;
  // 单次移动步长（相对槽位尺寸的比例），默认 0.02
  moveStep?: number;
  // 单次缩放步长（直接加减倍数），默认 0.1
  zoomStep?: number;
  // 缩放下限（倍数），默认 0.1
  zoomMin?: number;
  // 缩放上限（倍数），默认 5
  zoomMax?: number;
  // 是否按槽位覆盖限制最小缩放（开启后最小缩放不小于覆盖槽位的 1x）
  enforceMinZoomBySlotSize?: boolean;
  // 是否启用“分割线按线段独立拖拽”（启用：仅影响命中线段覆盖到的槽位；关闭：按整条分割线联动）
  independentDividerDrag?: boolean;
}

export interface PuzzleCanvasProps {
  layout: PuzzleLayout | null;
  slots: PuzzleSlot[];
  onImageUpload: (slotId: string, file: File) => void;
  onSlotsChange?: (slots: PuzzleSlot[]) => void; // 拖动分割线后更新槽位
  className?: string;
  settings?: PuzzleStyleSettings;
  // 拖拽画布边/角时更新比例（宽:高）。实现为连续值，最小为 1。
  onRatioChange?: (ratio: { w: number; h: number }) => void;
  // 画布像素尺寸变化通知（用于 UI 展示当前导出像素大小）
  onCanvasSizeChange?: (size: { width: number; height: number }) => void;
  // 选择/合并模式
  selectionEnabled?: boolean;
  selectedSlotIds?: string[];
  onSlotSelectToggle?: (slotId: string) => void;
  onClearSelection?: () => void;
}

export interface PuzzleTemplatesProps {
  onLayoutSelect: (layout: PuzzleLayout) => void;
  selectedLayout: PuzzleLayout | null;
  className?: string;
}
