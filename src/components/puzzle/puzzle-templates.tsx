'use client';

import { useEffect, useState } from 'react';
import { PuzzleLayout, PuzzleTemplatesProps } from './types';
import { X } from 'lucide-react';

// localStorage key for saving custom layouts
const CUSTOM_LAYOUTS_KEY = 'puzzle:customLayouts';

// 生成布局的辅助方法
const rowsOf = (colsPerRow: number[]) => {
  const rows = Math.max(1, colsPerRow.length);
  const slots: { x: number; y: number; width: number; height: number }[] = [];
  let y = 0;
  for (let r = 0; r < rows; r++) {
    const h = r === rows - 1 ? Math.max(0, 1 - y) : 1 / rows;
    const cols = Math.max(1, colsPerRow[r] || 1);
    let x = 0;
    for (let c = 0; c < cols; c++) {
      const w = c === cols - 1 ? Math.max(0, 1 - x) : 1 / cols;
      slots.push({ x, y, width: w, height: h });
      x += w;
    }
    y += h;
  }
  return slots;
};

const grid = (rows: number, cols: number) => rowsOf(Array.from({ length: Math.max(1, rows) }, () => Math.max(1, cols)));

// 列模式：按列分割，每列再按给定的行数等分
const colsOf = (rowsPerCol: number[]) => {
  const cols = Math.max(1, rowsPerCol.length);
  const slots: { x: number; y: number; width: number; height: number }[] = [];
  let x = 0;
  for (let c = 0; c < cols; c++) {
    const w = c === cols - 1 ? Math.max(0, 1 - x) : 1 / cols;
    const rows = Math.max(1, rowsPerCol[c] || 1);
    let y = 0;
    for (let r = 0; r < rows; r++) {
      const h = r === rows - 1 ? Math.max(0, 1 - y) : 1 / rows;
      slots.push({ x, y, width: w, height: h });
      y += h;
    }
    x += w;
  }
  return slots;
};

// 所有布局模板 - 严格按照设计图
export const allLayouts: PuzzleLayout[] = [
  // 1张图片
  {
    id: '1-layout-1',
    name: '',
    imageCount: 1,
    slots: [
      { x: 0, y: 0, width: 1, height: 1 }
    ]
  },

  // 2张图片
  {
    id: '2-layout-1',
    name: '',
    imageCount: 2,
    slots: [
      { x: 0, y: 0, width: 1, height: 1/2 },
      { x: 0, y: 1/2, width: 1, height: 1/2 }
    ]
  },
  {
    id: '2-layout-2',
    name: '',
    imageCount: 2,
    slots: [
      { x: 0, y: 0, width: 1/2, height: 1 },
      { x: 1/2, y: 0, width: 1/2, height: 1 }
    ]
  },

  // 3张图片
  {
    id: '3-layout-1',
    name: '',
    imageCount: 3,
    slots: [
      // 顶部一张铺满，底部两张左右平分
      { x: 0, y: 0, width: 1, height: 1/2 },
      { x: 0, y: 1/2, width: 1/2, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/2, height: 1/2 }
    ]
  },
  
  {
    id: '3-layout-2',
    name: '',
    imageCount: 3,
    slots: [
      // 左侧两张上下平分，右侧一张通栏
      { x: 0, y: 0, width: 1/2, height: 1/2 },
      { x: 0, y: 1/2, width: 1/2, height: 1/2 },
      { x: 1/2, y: 0, width: 1/2, height: 1 }
    ]
  },
  {
    id: '3-layout-3',
    name: '',
    imageCount: 3,
    slots: [
      // 顶部两张左右平分，底部一张通栏
      { x: 0, y: 0, width: 1/2, height: 1/2 },
      { x: 1/2, y: 0, width: 1/2, height: 1/2 },
      { x: 0, y: 1/2, width: 1, height: 1/2 }
    ]
  },
  {
    id: '3-layout-4',
    name: '',
    imageCount: 3,
    slots: [
      { x: 0, y: 0, width: 1/2, height: 1 },
      { x: 1/2, y: 0, width: 1/2, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/2, height: 1/2 }
    ]
  },  
  {
    id: '3-layout-5',
    name: '',
    imageCount: 3,
    slots: [
      // 三列等分
      { x: 0, y: 0, width: 1/3, height: 1 },
      { x: 1/3, y: 0, width: 1/3, height: 1 },
      { x: 2/3, y: 0, width: 1/3, height: 1 }
    ]
  },
  {
    id: '3-layout-6',
    name: '',
    imageCount: 3,
    slots: [
      // 三行等分
      { x: 0, y: 0, width: 1, height: 1/3 },
      { x: 0, y: 1/3, width: 1, height: 1/3 },
      { x: 0, y: 2/3, width: 1, height: 1/3 }
    ]
  },

  // 4张图片
  {
    id: '4-layout-1',
    name: '',
    imageCount: 4,
    slots: [
      // 2x2 等分
      { x: 0, y: 0, width: 1/2, height: 1/2 },
      { x: 1/2, y: 0, width: 1/2, height: 1/2 },
      { x: 0, y: 1/2, width: 1/2, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/2, height: 1/2 }
    ]
  },
  {
    id: '4-layout-2',
    name: '',
    imageCount: 4,
    slots: [
      // 四列等分
      { x: 0, y: 0, width: 1/4, height: 1 },
      { x: 1/4, y: 0, width: 1/4, height: 1 },
      { x: 1/2, y: 0, width: 1/4, height: 1 },
      { x: 3/4, y: 0, width: 1/4, height: 1 }
    ]
  },
  {
    id: '4-layout-3',
    name: '',
    imageCount: 4,
    slots: [
      // 四行等分
      { x: 0, y: 0, width: 1, height: 1/4 },
      { x: 0, y: 1/4, width: 1, height: 1/4 },
      { x: 0, y: 1/2, width: 1, height: 1/4 },
      { x: 0, y: 3/4, width: 1, height: 1/4 }
    ]
  },
  {
    id: '4-layout-4',
    name: '',
    imageCount: 4,
    slots: [
      // 顶部一张，底部三列
      { x: 0, y: 0, width: 1, height: 1/2 },
      { x: 0, y: 1/2, width: 1/3, height: 1/2 },
      { x: 1/3, y: 1/2, width: 1/3, height: 1/2 },
      { x: 2/3, y: 1/2, width: 1/3, height: 1/2 }
    ]
  },
  {
    id: '4-layout-5',
    name: '',
    imageCount: 4,
    slots: [
      // 顶部三列，底部一张
      { x: 0, y: 0, width: 1/3, height: 1/2 },
      { x: 1/3, y: 0, width: 1/3, height: 1/2 },
      { x: 2/3, y: 0, width: 1/3, height: 1/2 },
      { x: 0, y: 1/2, width: 1, height: 1/2 }
    ]
  },
  {
    id: '4-layout-6',
    name: '',
    imageCount: 4,
    slots: [
      // 三行：上、下通栏；中间一行左右分
      { x: 0, y: 0, width: 1, height: 1/3 },
      { x: 0, y: 1/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/2, height: 1/3 },
      { x: 0, y: 2/3, width: 1, height: 1/3 }
    ]
  },
  {
    id: '4-layout-7',
    name: '',
    imageCount: 4,
    slots: [
      // 三列：左、右通栏；中间一列上下分
      { x: 0, y: 0, width: 1/3, height: 1 },
      { x: 1/3, y: 0, width: 1/3, height: 1/2 },
      { x: 1/3, y: 1/2, width: 1/3, height: 1/2 },
      { x: 2/3, y: 0, width: 1/3, height: 1 }
    ]
  },
  {
    id: '4-layout-8',
    name: '',
    imageCount: 4,
    slots: [
      // 左侧通栏，右侧上一个、下两个
      { x: 0, y: 0, width: 1/2, height: 1 },
      { x: 1/2, y: 0, width: 1/2, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/4, height: 1/2 },
      { x: 3/4, y: 1/2, width: 1/4, height: 1/2 }
    ]
  },
  
  // 5张图片
  {
    id: '5-layout-1',
    name: '',
    imageCount: 5,
    slots: [
      // 行模式：上两列、下三列 (2 | 3)
      { x: 0, y: 0, width: 1/2, height: 1/2 },
      { x: 1/2, y: 0, width: 1/2, height: 1/2 },
      { x: 0, y: 1/2, width: 1/3, height: 1/2 },
      { x: 1/3, y: 1/2, width: 1/3, height: 1/2 },
      { x: 2/3, y: 1/2, width: 1/3, height: 1/2 }
    ]
  },
  {
    id: '5-layout-2',
    name: '',
    imageCount: 5,
    slots: [
      // 列模式：2行 | 1行 | 2行 (2,1,2)
      { x: 0, y: 0, width: 1/3, height: 1/2 },
      { x: 0, y: 1/2, width: 1/3, height: 1/2 },
      { x: 1/3, y: 0, width: 1/3, height: 1 },
      { x: 2/3, y: 0, width: 1/3, height: 1/2 },
      { x: 2/3, y: 1/2, width: 1/3, height: 1/2 }
    ]
  },
  {
    id: '5-layout-3',
    name: '',
    imageCount: 5,
    slots: [
      // 左侧通栏；右侧四行 (1 | 4)
      { x: 0, y: 0, width: 1/2, height: 1 },
      { x: 1/2, y: 0, width: 1/2, height: 1/4 },
      { x: 1/2, y: 1/4, width: 1/2, height: 1/4 },
      { x: 1/2, y: 1/2, width: 1/2, height: 1/4 },
      { x: 1/2, y: 3/4, width: 1/2, height: 1/4 }
    ]
  },
  {
    id: '5-layout-4',
    name: '',
    imageCount: 5,
    slots: [
      // 行模式：上三列、下两列 (3 | 2)
      { x: 0, y: 0, width: 1/3, height: 1/2 },
      { x: 1/3, y: 0, width: 1/3, height: 1/2 },
      { x: 2/3, y: 0, width: 1/3, height: 1/2 },
      { x: 0, y: 1/2, width: 1/2, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/2, height: 1/2 }
    ]
  },
  {
    id: '5-layout-5',
    name: '',
    imageCount: 5,
    slots: [
      // 行模式：两列 | 通栏 | 两列 (2 | 1 | 2)
      { x: 0, y: 0, width: 1/2, height: 1/3 },
      { x: 1/2, y: 0, width: 1/2, height: 1/3 },
      { x: 0, y: 1/3, width: 1, height: 1/3 },
      { x: 0, y: 2/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/2, height: 1/3 }
    ]
  },
  {
    id: '5-layout-6',
    name: '',
    imageCount: 5,
    slots: [
      // 列模式：通栏 | 三行 | 通栏 (1 | 3 | 1)
      { x: 0, y: 0, width: 1/3, height: 1 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 2/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1 }
    ]
  },
  {
    id: '5-layout-7',
    name: '',
    imageCount: 5,
    slots: [
      // 列模式：三行 | 两行 (3 | 2)
      { x: 0, y: 0, width: 1/2, height: 1/3 },
      { x: 0, y: 1/3, width: 1/2, height: 1/3 },
      { x: 0, y: 2/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 0, width: 1/2, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/2, height: 1/2 }
    ]
  },
  {
    id: '5-layout-8',
    name: '',
    imageCount: 5,
    slots: [
      // 3x3 网格：
      // 1) (r1,c1-2)  2) (r1-2,c3)  3) (r2-3,c1)  4) (r2,c2)  5) (r3,c2-3)
      // 按阅读顺序排序：1,2,3,4,5
      { x: 0, y: 0, width: 2/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 2/3 },
      { x: 0, y: 1/3, width: 1/3, height: 2/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 2/3, width: 2/3, height: 1/3 }
    ]
  },

  // 6张图片
  {
    id: '6-layout-1',
    name: '',
    imageCount: 6,
    slots: [
      // 等分 3x2 网格
      { x: 0, y: 0, width: 1/3, height: 1/2 },
      { x: 1/3, y: 0, width: 1/3, height: 1/2 },
      { x: 2/3, y: 0, width: 1/3, height: 1/2 },
      { x: 0, y: 1/2, width: 1/3, height: 1/2 },
      { x: 1/3, y: 1/2, width: 1/3, height: 1/2 },
      { x: 2/3, y: 1/2, width: 1/3, height: 1/2 }
    ]
  },
  {
    id: '6-layout-2',
    name: '',
    imageCount: 6,
    slots: [
      // 左半通栏 | 右半：上 3 列、下 2 列
      { x: 0, y: 0, width: 1/2, height: 1 },
      { x: 1/2, y: 0, width: 1/6, height: 1/2 },
      { x: 2/3, y: 0, width: 1/6, height: 1/2 },
      { x: 5/6, y: 0, width: 1/6, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/4, height: 1/2 },
      { x: 3/4, y: 1/2, width: 1/4, height: 1/2 }
    ]
  },
  {
    id: '6-layout-3',
    name: '',
    imageCount: 6,
    slots: [
      // 上：通栏；中：三列；下：两列
      { x: 0, y: 0, width: 1, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/2, height: 1/3 }
    ]
  },
  {
    id: '6-layout-4',
    name: '',
    imageCount: 6,
    slots: [
      // 列模式：左三行 | 中通栏 | 右两行 (3 | 1 | 2)
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1 },
      { x: 2/3, y: 0, width: 1/3, height: 1/2 },
      { x: 2/3, y: 1/2, width: 1/3, height: 1/2 }
    ]
  },
  {
    id: '6-layout-5',
    name: '',
    imageCount: 6,
    slots: [
      // 3x3 复合：顶三单元 | 中左单元 | 大块(右下 2x2) | 底左单元
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 2/3, height: 2/3 },
      { x: 0, y: 2/3, width: 1/3, height: 1/3 }
    ]
  },
  {
    id: '6-layout-6',
    name: '',
    imageCount: 6,
    slots: [
      // 上三列 | 中两列 | 下通栏 (3 | 2 | 1)
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/2, height: 1/3 },
      { x: 0, y: 2/3, width: 1, height: 1/3 }
    ]
  },
  {
    id: '6-layout-7',
    name: '',
    imageCount: 6,
    slots: [
      // 上两列 | 下四列 (2 | 4)
      { x: 0, y: 0, width: 1/2, height: 1/2 },
      { x: 1/2, y: 0, width: 1/2, height: 1/2 },
      { x: 0, y: 1/2, width: 1/4, height: 1/2 },
      { x: 1/4, y: 1/2, width: 1/4, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/4, height: 1/2 },
      { x: 3/4, y: 1/2, width: 1/4, height: 1/2 }
    ]
  },
  {
    id: '6-layout-8',
    name: '',
    imageCount: 6,
    slots: [
      // 上通栏 | 中四列 | 下通栏 (1 | 4 | 1)
      { x: 0, y: 0, width: 1, height: 1/3 },
      { x: 0, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 0, y: 2/3, width: 1, height: 1/3 }
    ]
  },

  // 7张图片
  {
    id: '7-layout-1',
    name: '',
    imageCount: 7,
    slots: [
      // 行：3列 | 2列 | 2列 (3 | 2 | 2)
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/2, height: 1/3 },
      { x: 0, y: 2/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/2, height: 1/3 }
    ]
  },
  {
    id: '7-layout-2',
    name: '',
    imageCount: 7,
    slots: [
      // 左一列通栏 | 右侧三行各两列 (1 | 2,2,2)
      { x: 0, y: 0, width: 1/3, height: 1 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 2/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 2/3, width: 1/3, height: 1/3 }
    ]
  },
  {
    id: '7-layout-3',
    name: '',
    imageCount: 7,
    slots: [
      // 行：3列 | 3列 | 通栏 (3 | 3 | 1)
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1, height: 1/3 }
    ]
  },
  {
    id: '7-layout-4',
    name: '',
    imageCount: 7,
    slots: [
      // 列：3行 | 3行 | 通栏 (3 | 3 | 1)
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 2/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1 }
    ]
  },
  {
    id: '7-layout-5',
    name: '',
    imageCount: 7,
    slots: [
      // 左：上跨两行 + 下单元 | 右：上通栏 + 中2列 + 下2列
      { x: 0, y: 0, width: 1/2, height: 2/3 },
      { x: 0, y: 2/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 0, width: 1/2, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 2/3, width: 1/4, height: 1/3 }
    ]
  },
  {
    id: '7-layout-6',
    name: '',
    imageCount: 7,
    slots: [
      // 左右侧边栏通栏 (2) | 中部三行：2 | 2 | 1 (合计7)
      { x: 0, y: 0, width: 1/5, height: 1 },
      { x: 4/5, y: 0, width: 1/5, height: 1 },
      { x: 1/5, y: 0, width: 3/10, height: 1/3 },
      { x: 1/2, y: 0, width: 3/10, height: 1/3 },
      { x: 1/5, y: 1/3, width: 3/10, height: 1/3 },
      { x: 1/2, y: 1/3, width: 3/10, height: 1/3 },
      { x: 1/5, y: 2/3, width: 3/5, height: 1/3 }
    ]
  },
  {
    id: '7-layout-7',
    name: '',
    imageCount: 7,
    slots: [
      // 四行：3 | 2 | 1 | 1
      { x: 0, y: 0, width: 1/3, height: 1/4 },
      { x: 1/3, y: 0, width: 1/3, height: 1/4 },
      { x: 2/3, y: 0, width: 1/3, height: 1/4 },
      { x: 0, y: 1/4, width: 1/2, height: 1/4 },
      { x: 1/2, y: 1/4, width: 1/2, height: 1/4 },
      { x: 0, y: 1/2, width: 1, height: 1/4 },
      { x: 0, y: 3/4, width: 1, height: 1/4 }
    ]
  },
  {
    id: '7-layout-8',
    name: '',
    imageCount: 7,
    slots: [
      // 四列：2 | 2 | 2 | 1
      { x: 0, y: 0, width: 1/4, height: 1/2 },
      { x: 0, y: 1/2, width: 1/4, height: 1/2 },
      { x: 1/4, y: 0, width: 1/4, height: 1/2 },
      { x: 1/4, y: 1/2, width: 1/4, height: 1/2 },
      { x: 1/2, y: 0, width: 1/4, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/4, height: 1/2 },
      { x: 3/4, y: 0, width: 1/4, height: 1 }
    ]
  },

  // 8张图片
  {
    id: '8-layout-1',
    name: '',
    imageCount: 8,
    slots: [
      // 4x2 等分
      { x: 0, y: 0, width: 1/4, height: 1/2 },
      { x: 1/4, y: 0, width: 1/4, height: 1/2 },
      { x: 1/2, y: 0, width: 1/4, height: 1/2 },
      { x: 3/4, y: 0, width: 1/4, height: 1/2 },
      { x: 0, y: 1/2, width: 1/4, height: 1/2 },
      { x: 1/4, y: 1/2, width: 1/4, height: 1/2 },
      { x: 1/2, y: 1/2, width: 1/4, height: 1/2 },
      { x: 3/4, y: 1/2, width: 1/4, height: 1/2 }
    ]
  },
  {
    id: '8-layout-2',
    name: '',
    imageCount: 8,
    slots: [
      // 2x4 等分
      { x: 0, y: 0, width: 1/2, height: 1/4 },
      { x: 1/2, y: 0, width: 1/2, height: 1/4 },
      { x: 0, y: 1/4, width: 1/2, height: 1/4 },
      { x: 1/2, y: 1/4, width: 1/2, height: 1/4 },
      { x: 0, y: 1/2, width: 1/2, height: 1/4 },
      { x: 1/2, y: 1/2, width: 1/2, height: 1/4 },
      { x: 0, y: 3/4, width: 1/2, height: 1/4 },
      { x: 1/2, y: 3/4, width: 1/2, height: 1/4 }
    ]
  },
  {
    id: '8-layout-3',
    name: '',
    imageCount: 8,
    slots: [
      // 行：3 | 3 | 2
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/2, height: 1/3 }
    ]
  },
  {
    id: '8-layout-4',
    name: '',
    imageCount: 8,
    slots: [
      // 列：3 | 3 | 2
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 2/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/2 },
      { x: 2/3, y: 1/2, width: 1/3, height: 1/2 }
    ]
  },
  {
    id: '8-layout-5',
    name: '',
    imageCount: 8,
    slots: [
      // 行：2 | 4 | 2
      { x: 0, y: 0, width: 1/2, height: 1/3 },
      { x: 1/2, y: 0, width: 1/2, height: 1/3 },
      { x: 0, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 0, y: 2/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/2, height: 1/3 }
    ]
  },
  {
    id: '8-layout-6',
    name: '',
    imageCount: 8,
    slots: [
      // 列：1 | 2 | 3 | 2
      { x: 0, y: 0, width: 1/4, height: 1 },
      { x: 1/4, y: 0, width: 1/4, height: 1/2 },
      { x: 1/4, y: 1/2, width: 1/4, height: 1/2 },
      { x: 1/2, y: 0, width: 1/4, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 0, width: 1/4, height: 1/2 },
      { x: 3/4, y: 1/2, width: 1/4, height: 1/2 }
    ]
  },
  {
    id: '8-layout-7',
    name: '',
    imageCount: 8,
    slots: [
      // 行：1 | 6 | 1（上/下通栏 + 中行六列）
      { x: 0, y: 0, width: 1, height: 1/4 },
      { x: 0, y: 1/4, width: 1/6, height: 1/2 },
      { x: 1/6, y: 1/4, width: 1/6, height: 1/2 },
      { x: 1/3, y: 1/4, width: 1/6, height: 1/2 },
      { x: 1/2, y: 1/4, width: 1/6, height: 1/2 },
      { x: 2/3, y: 1/4, width: 1/6, height: 1/2 },
      { x: 5/6, y: 1/4, width: 1/6, height: 1/2 },
      { x: 0, y: 3/4, width: 1, height: 1/4 }
    ]
  },
  {
    id: '8-layout-8',
    name: '',
    imageCount: 8,
    slots: [
      // 侧边两列通栏 | 中间三行各两列 (2 + 6)
      { x: 0, y: 0, width: 1/4, height: 1 },
      { x: 3/4, y: 0, width: 1/4, height: 1 },
      { x: 1/4, y: 0, width: 1/4, height: 1/3 },
      { x: 1/2, y: 0, width: 1/4, height: 1/3 },
      { x: 1/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/4, height: 1/3 }
    ]
  },

  // 9张图片
  {
    id: '9-layout-1',
    name: '',
    imageCount: 9,
    slots: [
      // 3x3 等分
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 2/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 2/3, width: 1/3, height: 1/3 }
    ]
  },
  {
    id: '9-layout-2',
    name: '',
    imageCount: 9,
    slots: [
      // 行：2 | 3 | 4
      { x: 0, y: 0, width: 1/2, height: 1/3 },
      { x: 1/2, y: 0, width: 1/2, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 2/3, width: 1/4, height: 1/3 }
    ]
  },
  {
    id: '9-layout-3',
    name: '',
    imageCount: 9,
    slots: [
      // 列：1 | 4 | 4（按列分配高度）
      { x: 0, y: 0, width: 1/3, height: 1 },
      { x: 1/3, y: 0, width: 1/3, height: 1/4 },
      { x: 1/3, y: 1/4, width: 1/3, height: 1/4 },
      { x: 1/3, y: 1/2, width: 1/3, height: 1/4 },
      { x: 1/3, y: 3/4, width: 1/3, height: 1/4 },
      { x: 2/3, y: 0, width: 1/3, height: 1/4 },
      { x: 2/3, y: 1/4, width: 1/3, height: 1/4 },
      { x: 2/3, y: 1/2, width: 1/3, height: 1/4 },
      { x: 2/3, y: 3/4, width: 1/3, height: 1/4 }
    ]
  },
  {
    id: '9-layout-4',
    name: '',
    imageCount: 9,
    slots: [
      // 行：1 | 4 | 4（上通栏 + 两行四列）
      { x: 0, y: 0, width: 1, height: 1/3 },
      { x: 0, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 0, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 2/3, width: 1/4, height: 1/3 }
    ]
  },
  {
    id: '9-layout-5',
    name: '',
    imageCount: 9,
    slots: [
      // 列：4 | 3 | 2（每列不同分割）
      { x: 0, y: 0, width: 1/3, height: 1/4 },
      { x: 0, y: 1/4, width: 1/3, height: 1/4 },
      { x: 0, y: 1/2, width: 1/3, height: 1/4 },
      { x: 0, y: 3/4, width: 1/3, height: 1/4 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 2/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/2 },
      { x: 2/3, y: 1/2, width: 1/3, height: 1/2 }
    ]
  },
  {
    id: '9-layout-6',
    name: '',
    imageCount: 9,
    slots: [
      // 行：4 | 3 | 2
      { x: 0, y: 0, width: 1/4, height: 1/3 },
      { x: 1/4, y: 0, width: 1/4, height: 1/3 },
      { x: 1/2, y: 0, width: 1/4, height: 1/3 },
      { x: 3/4, y: 0, width: 1/4, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/2, height: 1/3 }
    ]
  },
  {
    id: '9-layout-7',
    name: '',
    imageCount: 9,
    slots: [
      // 行：3 | 2 | 4
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/2, height: 1/3 },
      { x: 0, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 2/3, width: 1/4, height: 1/3 }
    ]
  },

  {
    id: '9-layout-8',
    name: '',
    imageCount: 9,
    slots: [
      // 6x6 网格：3x3 大块×3 + 2x2 ×1 + 1x1 ×5（阅读顺序）
      // 三个 3x3 大块（各占 3/6 = 0.5）
      { x: 0, y: 0, width: 1/2, height: 1/2 },
      { x: 1/2, y: 0, width: 1/2, height: 1/2 },
      { x: 0, y: 1/2, width: 1/2, height: 1/2 },
      // 一个 2x2 区块（占 2/6 = 0.3333）
      { x: 1/2, y: 1/2, width: 1/3, height: 1/3 },
      // 1x1 单元（各占 1/6 = 0.1667）
      { x: 5/6, y: 1/2, width: 1/6, height: 1/6 },
      { x: 5/6, y: 2/3, width: 1/6, height: 1/6 },
      { x: 1/2, y: 5/6, width: 1/6, height: 1/6 },
      { x: 2/3, y: 5/6, width: 1/6, height: 1/6 },
      { x: 5/6, y: 5/6, width: 1/6, height: 1/6 }
    ]
  },

  // 10张图片
  {
    id: '10-layout-1',
    name: '',
    imageCount: 10,
    slots: [
      // 5x2 等分
      { x: 0, y: 0, width: 1/5, height: 1/2 },
      { x: 1/5, y: 0, width: 1/5, height: 1/2 },
      { x: 2/5, y: 0, width: 1/5, height: 1/2 },
      { x: 3/5, y: 0, width: 1/5, height: 1/2 },
      { x: 4/5, y: 0, width: 1/5, height: 1/2 },
      { x: 0, y: 1/2, width: 1/5, height: 1/2 },
      { x: 1/5, y: 1/2, width: 1/5, height: 1/2 },
      { x: 2/5, y: 1/2, width: 1/5, height: 1/2 },
      { x: 3/5, y: 1/2, width: 1/5, height: 1/2 },
      { x: 4/5, y: 1/2, width: 1/5, height: 1/2 }
    ]
  },
  {
    id: '10-layout-2',
    name: '',
    imageCount: 10,
    slots: [
      // 2x5 等分
      { x: 0, y: 0, width: 1/2, height: 1/5 },
      { x: 1/2, y: 0, width: 1/2, height: 1/5 },
      { x: 0, y: 1/5, width: 1/2, height: 1/5 },
      { x: 1/2, y: 1/5, width: 1/2, height: 1/5 },
      { x: 0, y: 2/5, width: 1/2, height: 1/5 },
      { x: 1/2, y: 2/5, width: 1/2, height: 1/5 },
      { x: 0, y: 3/5, width: 1/2, height: 1/5 },
      { x: 1/2, y: 3/5, width: 1/2, height: 1/5 },
      { x: 0, y: 4/5, width: 1/2, height: 1/5 },
      { x: 1/2, y: 4/5, width: 1/2, height: 1/5 }
    ]
  },
  {
    id: '10-layout-3',
    name: '',
    imageCount: 10,
    slots: [
      // 行：3 | 4 | 3
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 1/3, width: 1/4, height: 1/3 },
      { x: 0, y: 2/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 2/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 2/3, width: 1/3, height: 1/3 }
    ]
  },
  {
    id: '10-layout-4',
    name: '',
    imageCount: 10,
    slots: [
      // 列：3 | 4 | 3（按列分配）
      { x: 0, y: 0, width: 1/3, height: 1/3 },
      { x: 0, y: 1/3, width: 1/3, height: 1/3 },
      { x: 0, y: 2/3, width: 1/3, height: 1/3 },
      { x: 1/3, y: 0, width: 1/3, height: 1/4 },
      { x: 1/3, y: 1/4, width: 1/3, height: 1/4 },
      { x: 1/3, y: 1/2, width: 1/3, height: 1/4 },
      { x: 1/3, y: 3/4, width: 1/3, height: 1/4 },
      { x: 2/3, y: 0, width: 1/3, height: 1/3 },
      { x: 2/3, y: 1/3, width: 1/3, height: 1/3 },
      { x: 2/3, y: 2/3, width: 1/3, height: 1/3 }
    ]
  },
  {
    id: '10-layout-5',
    name: '',
    imageCount: 10,
    slots: [
      // 行：1 | 8 | 1（上/下通栏 + 中两行四列）
      { x: 0, y: 0, width: 1, height: 1/5 },
      { x: 0, y: 1/5, width: 1/4, height: 3/10 },
      { x: 1/4, y: 1/5, width: 1/4, height: 3/10 },
      { x: 1/2, y: 1/5, width: 1/4, height: 3/10 },
      { x: 3/4, y: 1/5, width: 1/4, height: 3/10 },
      { x: 0, y: 1/2, width: 1/4, height: 3/10 },
      { x: 1/4, y: 1/2, width: 1/4, height: 3/10 },
      { x: 1/2, y: 1/2, width: 1/4, height: 3/10 },
      { x: 3/4, y: 1/2, width: 1/4, height: 3/10 },
      { x: 0, y: 4/5, width: 1, height: 1/5 }
    ]
  },
  {
    id: '10-layout-6',
    name: '',
    imageCount: 10,
    slots: [
      // 行：4 | 2 | 4
      { x: 0, y: 0, width: 1/4, height: 1/3 },
      { x: 1/4, y: 0, width: 1/4, height: 1/3 },
      { x: 1/2, y: 0, width: 1/4, height: 1/3 },
      { x: 3/4, y: 0, width: 1/4, height: 1/3 },
      { x: 0, y: 1/3, width: 1/2, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/2, height: 1/3 },
      { x: 0, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 2/3, width: 1/4, height: 1/3 }
    ]
  },
  {
    id: '10-layout-7',
    name: '',
    imageCount: 10,
    slots: [
      // 左右侧边栏各通栏（2）| 中部 2x4（8）
      { x: 0, y: 0, width: 1/5, height: 1 },
      { x: 4/5, y: 0, width: 1/5, height: 1 },
      { x: 1/5, y: 0, width: 3/20, height: 1/2 },
      { x: 7/20, y: 0, width: 3/20, height: 1/2 },
      { x: 1/2, y: 0, width: 3/20, height: 1/2 },
      { x: 13/20, y: 0, width: 3/20, height: 1/2 },
      { x: 1/5, y: 1/2, width: 3/20, height: 1/2 },
      { x: 7/20, y: 1/2, width: 3/20, height: 1/2 },
      { x: 1/2, y: 1/2, width: 3/20, height: 1/2 },
      { x: 13/20, y: 1/2, width: 3/20, height: 1/2 }
    ]
  },
  {
    id: '10-layout-8',
    name: '',
    imageCount: 10,
    slots: [
      // 四列瀑布：3 | 2 | 3 | 2
      { x: 0, y: 0, width: 1/4, height: 1/3 },
      { x: 0, y: 1/3, width: 1/4, height: 1/3 },
      { x: 0, y: 2/3, width: 1/4, height: 1/3 },
      { x: 1/4, y: 0, width: 1/4, height: 1/2 },
      { x: 1/4, y: 1/2, width: 1/4, height: 1/2 },
      { x: 1/2, y: 0, width: 1/4, height: 1/3 },
      { x: 1/2, y: 1/3, width: 1/4, height: 1/3 },
      { x: 1/2, y: 2/3, width: 1/4, height: 1/3 },
      { x: 3/4, y: 0, width: 1/4, height: 1/2 },
      { x: 3/4, y: 1/2, width: 1/4, height: 1/2 }
    ]
  },
];

export default function PuzzleTemplates({ onLayoutSelect, selectedLayout, className = '' }: PuzzleTemplatesProps) {
  const [customs, setCustoms] = useState<PuzzleLayout[]>([]);
  const [rows, setRows] = useState<string>('2');
  const [cols, setCols] = useState<string>('2');
  // 行模式输入，例如 "3|1|2" 表示三行分别为 3、1、2 列
  const [rowsPattern, setRowsPattern] = useState<string>('');
  // 列模式输入，例如 "2|1|3" 表示三列分别为 2、1、3 行
  const [colsPattern, setColsPattern] = useState<string>('');

  // load custom layouts from localStorage
  useEffect(() => {
    try {
      const raw = localStorage.getItem(CUSTOM_LAYOUTS_KEY);
      if (!raw) return;
      const parsed = JSON.parse(raw);
      if (Array.isArray(parsed)) {
        const valid = parsed.filter((l: any) => l && Array.isArray(l.slots));
        setCustoms(valid);
      }
    } catch {}
  }, []);

  // 监听来自编辑器的保存事件，刷新自定义布局列表
  useEffect(() => {
    const handler = () => {
      try {
        const raw = localStorage.getItem(CUSTOM_LAYOUTS_KEY);
        const parsed = raw ? JSON.parse(raw) : [];
        if (Array.isArray(parsed)) {
          const valid = parsed.filter((l: any) => l && Array.isArray(l.slots));
          setCustoms(valid);
        }
      } catch {}
    };
    window.addEventListener('puzzle:customLayouts:updated', handler);
    return () => window.removeEventListener('puzzle:customLayouts:updated', handler);
  }, []);

  const saveCustoms = (next: PuzzleLayout[]) => {
    setCustoms(next);
    try { localStorage.setItem(CUSTOM_LAYOUTS_KEY, JSON.stringify(next)); } catch {}
  };

  const createGridLayout = () => {
    const r = Math.max(1, Math.floor(Number(rows) || 0));
    const c = Math.max(1, Math.floor(Number(cols) || 0));
    const id = `custom-${Date.now()}`;
    const name = `自定义 ${r}×${c}`;
    const slots = grid(r, c);
    const layout: PuzzleLayout = { id, name, imageCount: r * c, slots };
    const next = [...customs, layout];
    saveCustoms(next);
    onLayoutSelect(layout);
  };

  const createRowsPatternLayout = () => {
    // 允许形如 "3|1|2"，或用中文/空格/逗号分隔，做宽松解析
    const raw = rowsPattern.trim();
    if (!raw) return;
    const parts = raw
      .split(/[^\d]+/)
      .map((s) => s.trim())
      .filter(Boolean)
      .map((n) => Math.max(1, Math.floor(Number(n) || 0)));
    if (parts.length === 0) return;
    const id = `custom-${Date.now()}`;
    const name = `自定义 行模式 ${parts.join('|')}`;
    const slots = rowsOf(parts);
    const layout: PuzzleLayout = { id, name, imageCount: slots.length, slots };
    const next = [...customs, layout];
    saveCustoms(next);
    onLayoutSelect(layout);
  };

  const createColsPatternLayout = () => {
    // 允许形如 "2|1|3"，或用中文/空格/逗号分隔，做宽松解析
    const raw = colsPattern.trim();
    if (!raw) return;
    const parts = raw
      .split(/[^\d]+/)
      .map((s) => s.trim())
      .filter(Boolean)
      .map((n) => Math.max(1, Math.floor(Number(n) || 0)));
    if (parts.length === 0) return;
    const id = `custom-${Date.now()}`;
    const name = `自定义 列模式 ${parts.join('|')}`;
    const slots = colsOf(parts);
    const layout: PuzzleLayout = { id, name, imageCount: slots.length, slots };
    const next = [...customs, layout];
    saveCustoms(next);
    onLayoutSelect(layout);
  };

  const deleteCustom = (id: string) => {
    const next = customs.filter((l) => l.id !== id);
    saveCustoms(next);
  };

  const renderLayoutPreview = (layout: PuzzleLayout) => (
    <div className="w-full aspect-square bg-gray-100 dark:bg-gray-800 rounded relative overflow-hidden border border-gray-200 dark:border-gray-700">
      {layout.slots.map((slot, index) => (
        <div
          key={index}
          className={`absolute border border-white dark:border-gray-600 ${
            selectedLayout?.id === layout.id
              ? 'bg-blue-600 dark:bg-blue-700'
              : 'bg-blue-200 dark:bg-blue-800'
          }`}
          style={{
            left: `${slot.x * 100}%`,
            top: `${slot.y * 100}%`,
            width: `${slot.width * 100}%`,
            height: `${slot.height * 100}%`
          }}
        />
      ))}
    </div>
  );

  // 按图片数量分组
  const layoutsByCount = allLayouts.reduce((acc, layout) => {
    const count = layout.imageCount;
    if (!acc[count]) acc[count] = [];
    acc[count].push(layout);
    return acc;
  }, {} as Record<number, PuzzleLayout[]>);

  return (
    <div className={`flex flex-col h-full min-h-0 ${className}`}>
      {/* Custom creator */}
      <div className="p-2 border-b border-gray-200 dark:border-gray-800">
        <div className="text-xs font-medium text-slate-700 dark:text-slate-200 mb-1.5">自定义布局</div>
        <div className="flex items-end gap-2">
          <div className="flex items-center gap-1">
            <span className="text-[11px] text-slate-500">行</span>
            <input value={rows} onChange={(e) => setRows(e.target.value)} type="number" min={1} className="h-7 w-16 px-2 text-xs border rounded bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700" />
          </div>
          <div className="flex items-center gap-1">
            <span className="text-[11px] text-slate-500">列</span>
            <input value={cols} onChange={(e) => setCols(e.target.value)} type="number" min={1} className="h-7 w-16 px-2 text-xs border rounded bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700" />
          </div>
          <button className="h-7 px-2 text-xs rounded bg-blue-600 text-white hover:bg-blue-700" onClick={createGridLayout}>生成</button>
        </div>
        {/* 行模式创建器 */}
        <div className="mt-2 flex items-end gap-2">
          <div className="flex items-center gap-1 flex-1">
            <span className="text-[11px] text-slate-500 whitespace-nowrap">行模式</span>
            <input
              value={rowsPattern}
              onChange={(e) => setRowsPattern(e.target.value)}
              placeholder="如 3|1|2 或 4,2,2"
              className="h-7 px-2 text-xs border rounded bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 w-full"
            />
          </div>
          <button className="h-7 px-2 text-xs rounded bg-indigo-600 text-white hover:bg-indigo-700" onClick={createRowsPatternLayout}>生成</button>
        </div>
        {/* 列模式创建器 */}
        <div className="mt-2 flex items-end gap-2">
          <div className="flex items-center gap-1 flex-1">
            <span className="text-[11px] text-slate-500 whitespace-nowrap">列模式</span>
            <input
              value={colsPattern}
              onChange={(e) => setColsPattern(e.target.value)}
              placeholder="如 2|1|3 或 1,3,2"
              className="h-7 px-2 text-xs border rounded bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 w-full"
            />
          </div>
          <button className="h-7 px-2 text-xs rounded bg-violet-600 text-white hover:bg-violet-700" onClick={createColsPatternLayout}>生成</button>
        </div>
        {customs.length > 0 && (
          <div className="mt-2">
            <div className="text-[11px] text-slate-500 mb-1">我的布局</div>
            <div className="grid grid-cols-4 gap-1.5">
              {customs.map((layout) => (
                <div
                  key={layout.id}
                  className={`relative cursor-pointer transition-all duration-200 ${
                    selectedLayout?.id === layout.id ? 'ring-2 ring-blue-500' : 'hover:ring-1 hover:ring-gray-300'
                  }`}
                  onClick={() => onLayoutSelect(layout)}
                >
                  {renderLayoutPreview(layout)}
                  <button
                    className="absolute -top-1 -right-1 bg-white/90 dark:bg-slate-900/90 rounded-full shadow p-0.5"
                    onClick={(e) => { e.stopPropagation(); deleteCustom(layout.id); }}
                    aria-label="删除自定义布局"
                  >
                    <X className="w-3.5 h-3.5 text-slate-500" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="flex-1 overflow-y-auto max-h-screen p-2">
        <div className="space-y-3">
          {Object.entries(layoutsByCount).map(([count, layouts]) => (
            <div key={count}>
              <div className="text-xs font-medium text-slate-600 dark:text-slate-400 mb-1.5">
                {count}张图片
              </div>
              <div className="grid grid-cols-4 gap-1.5">
                {layouts.map((layout) => (
                  <div
                    key={layout.id}
                    className={`cursor-pointer transition-all duration-200 ${
                      selectedLayout?.id === layout.id
                        ? 'ring-2 ring-blue-500'
                        : 'hover:ring-1 hover:ring-gray-300'
                    }`}
                    onClick={() => onLayoutSelect(layout)}
                  >
                    {renderLayoutPreview(layout)}
                  </div>
                ))}
              </div>
            </div>
          ))}
          <div className="pt-3 text-[11px] text-slate-500 dark:text-slate-400 border-t border-gray-200 dark:border-gray-800">
            如需更多布局，可上方使用「自定义布局」创建
          </div>
        </div>
      </div>
    </div>
  );
}
