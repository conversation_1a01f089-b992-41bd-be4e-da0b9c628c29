'use client';

import { forwardRef, useEffect, useRef, useImperativeHandle, useCallback, useMemo, useState } from 'react';
import { Upload, Plus, ZoomIn, ZoomOut, RotateCw, FlipHorizontal, FlipVertical, X, Undo2, ArrowUp, ArrowDown, ArrowLeft, ArrowRight, Crosshair, MoreHorizontal } from 'lucide-react';
import { PuzzleCanvasProps, PuzzleSlot } from './types';

const PuzzleCanvas = forwardRef<HTMLCanvasElement, PuzzleCanvasProps>(
  ({ layout, slots, onImageUpload, onSlotsChange, className = '', settings, onRatioChange, onCanvasSizeChange, selectionEnabled = false, selectedSlotIds = [], onSlotSelectToggle, onClearSelection }, ref) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const lastReportedSizeRef = useRef<{ width: number; height: number } | null>(null);
    // 用于抑制拖动分割条后产生的“点击”事件，避免误触发上传
    const suppressClickRef = useRef(false);
    const suppressTimerRef = useRef<number | null>(null);
    // 控制面板正在交互（按下未抬起）标记
    const controlActiveRef = useRef(false);
    // 记录按下是否始于槽位背景，用于严格限制触发上传的来源
    const slotClickStartRef = useRef<string | null>(null);
    const [dragging, setDragging] = useState<
      | null
      | (
          | {
              // 纵向拖拽（整条分割线联动）
              type: 'v';
              pos: number;
              lowerBound: number;
              upperBound: number;
            }
          | {
              // 纵向拖拽（线段独立）
              type: 'v';
              pos: number;
              lowerBound: number;
              upperBound: number;
              segTop: number;
              segBottom: number;
            }
          | {
              // 横向拖拽（整条分割线联动）
              type: 'h';
              pos: number;
              lowerBound: number;
              upperBound: number;
            }
          | {
              // 横向拖拽（线段独立）
              type: 'h';
              pos: number;
              lowerBound: number;
              upperBound: number;
              segLeft: number;
              segRight: number;
            }
        )
    >(null);

    // HTML5 拖拽用于图片交换
    const [dndSourceId, setDndSourceId] = useState<string | null>(null);
    const [dndTargetId, setDndTargetId] = useState<string | null>(null);

    // 悬停的分割线（用于仅在靠近时显示）
    const [hoveredDivider, setHoveredDivider] = useState<
      | null
      | (
          | { type: 'v'; pos: number } // 整条联动
          | { type: 'h'; pos: number }
          | { type: 'v'; pos: number; top: number; height: number } // 线段独立
          | { type: 'h'; pos: number; left: number; width: number }
        )
    >(null);
    // 悬停的画布边/角
    const [hoveredEdge, setHoveredEdge] = useState<
      | null
      | 'left'
      | 'right'
      | 'top'
      | 'bottom'
      | 'corner-nw'
      | 'corner-ne'
      | 'corner-se'
      | 'corner-sw'
    >(null);
    // 画布比例拖拽状态   
    const [ratioDragging, setRatioDragging] = useState<
      | null
      | {
          mode: 'left' | 'right' | 'top' | 'bottom' | 'corner-nw' | 'corner-ne' | 'corner-se' | 'corner-sw';
          startX: number;
          startY: number;
          initWpx: number;
          initHpx: number;
          initScaleX: number; // canvas px / container px（在拖拽开始时固定）
          initScaleY: number; // canvas px / container px（在拖拽开始时固定）
          initRatioW: number;
          initRatioH: number;
          centeredX: boolean; // 容器是否水平置中（需 2x 补偿）
          centeredY: boolean; // 容器是否垂直置中（需 2x 补偿）
        }
    >(null);

    // 自由缩放像素尺度：与 ratio 一起决定最终画布像素尺寸。
    // 为空时使用默认基准（最长边 600px）初始化；一旦拖拽调整后将保持为用户设定的尺度。
    const [canvasScale, setCanvasScale] = useState<number | null>(null);
    // 监测外部 ratio 变化：若非拖拽导致，则重置像素尺度，避免预设比例受之前自由缩放影响
    const prevRatioRef = useRef<{ w: number; h: number } | null>(null);
    useEffect(() => {
      const curr = { w: settings?.ratio?.w ?? 1, h: settings?.ratio?.h ?? 1 };
      const prev = prevRatioRef.current;
      const changed = !prev || prev.w !== curr.w || prev.h !== curr.h;
      // 仅当 ratio 真正变化且当前没有进行比例拖拽时才重置像素尺度
      if (changed && !ratioDragging) {
        setCanvasScale(null);
      }
      prevRatioRef.current = curr;
    }, [settings?.ratio, ratioDragging]);

    // 槽位内拖拽平移（pan）状态
    const [isPanning, setIsPanning] = useState(false);
    const panRef = useRef<
      | null
      | {
          slotId: string;
          startX: number;
          startY: number;
          baseOffX: number;
          baseOffY: number;
          slotWpx: number;
          slotHpx: number;
          maxDXr: number;
          maxDYr: number;
        }
    >(null);

    // 控制面板显隐（延迟隐藏以跨越微小缝隙）
    const [openPanelSlotId, setOpenPanelSlotId] = useState<string | null>(null);
    const panelHideTimerRef = useRef<number | null>(null);
    const openPanelNow = useCallback((id: string) => {
      if (panelHideTimerRef.current !== null) {
        window.clearTimeout(panelHideTimerRef.current);
        panelHideTimerRef.current = null;
      }
      setOpenPanelSlotId(id);
    }, []);
    const scheduleClosePanel = useCallback((id: string) => {
      if (panelHideTimerRef.current !== null) {
        window.clearTimeout(panelHideTimerRef.current);
        panelHideTimerRef.current = null;
      }
      panelHideTimerRef.current = window.setTimeout(() => {
        setOpenPanelSlotId((curr) => (curr === id ? null : curr));
        panelHideTimerRef.current = null;
      }, 150);
    }, []);

    useImperativeHandle(ref, () => canvasRef.current!);

    // 防止 Alt+滚轮时触发窗口滚动：在容器上注册非被动 wheel 监听，仅在按下 Alt 时阻止默认行为
    useEffect(() => {
      const el = containerRef.current;
      if (!el) return;
      const onWheel = (e: WheelEvent) => {
        if (e.altKey) {
          e.preventDefault();
        }
      };
      el.addEventListener('wheel', onWheel, { passive: false });
      return () => {
        el.removeEventListener('wheel', onWheel as EventListener);
      };
    }, []);

    // —— 移动步长（取消长按连续移动） ——
    const slotsRef = useRef(slots);
    useEffect(() => { slotsRef.current = slots; }, [slots]);

    // 组件卸载时清理面板隐藏定时器
    useEffect(() => {
      return () => {
        if (panelHideTimerRef.current !== null) {
          window.clearTimeout(panelHideTimerRef.current);
          panelHideTimerRef.current = null;
        }
      };
    }, []);

    const moveOnce = (slotId: string, dirX: number, dirY: number, multiplier = 1) => {
      if (!onSlotsChange) return;
      const target = slotsRef.current.find((ss) => ss.id === slotId);
      if (!target || !target.image) return;

      const base = settings?.moveStep ?? 0.02;
      const step = base * multiplier;

      // 计算当前槽位像素尺寸，与 draw() 保持一致
      const size = computeCanvasSize();
      const padding = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
      const gap = settings?.gap ?? 0;
      const innerW = Math.max(1, size.width - (padding.left + padding.right));
      const innerH = Math.max(1, size.height - (padding.top + padding.bottom));
      const slotW = Math.max(0, target.width * innerW - gap);
      const slotH = Math.max(0, target.height * innerH - gap);

      // 计算覆盖所需尺寸（考虑 90/270 度时 cover 轴互换）
      const imgAspect = target.image.width / target.image.height;
      const ang = ((target.rotation % 360) + 360) % 360; // 0..359
      const rotated = ang % 180 !== 0;
      const coverW = rotated ? slotH : slotW;
      const coverH = rotated ? slotW : slotH;
      let baseW: number, baseH: number;
      if (imgAspect > coverW / coverH) {
        baseH = coverH;
        baseW = baseH * imgAspect;
      } else {
        baseW = coverW;
        baseH = baseW / imgAspect;
      }
      const minSetting = settings?.zoomMin ?? 0.1;
      const minZoom = settings?.enforceMinZoomBySlotSize ? Math.max(1, minSetting) : minSetting;
      const zoom = Math.max(minZoom, target.zoom || 1);
      const drawW = baseW * zoom;
      const drawH = baseH * zoom;

      // 旋转后的轴对齐包围盒大小（用于限制偏移，保证不露底）
      const theta = (ang * Math.PI) / 180;
      const cosT = Math.abs(Math.cos(theta));
      const sinT = Math.abs(Math.sin(theta));
      const aabbW = drawW * cosT + drawH * sinT;
      const aabbH = drawW * sinT + drawH * cosT;
      const maxDXpx = Math.max(0, (aabbW - slotW) / 2);
      const maxDYpx = Math.max(0, (aabbH - slotH) / 2);
      const maxDXr = slotW > 0 ? maxDXpx / slotW : 0; // 相对槽位宽度
      const maxDYr = slotH > 0 ? maxDYpx / slotH : 0; // 相对槽位高度

      const nextX = (target.offsetX ?? 0) + dirX * step;
      const nextY = (target.offsetY ?? 0) + dirY * step;
      const clampedX = Math.max(-maxDXr, Math.min(maxDXr, nextX));
      const clampedY = Math.max(-maxDYr, Math.min(maxDYr, nextY));

      const updated: PuzzleSlot[] = slotsRef.current.map((s) =>
        s.id === slotId ? { ...s, offsetX: clampedX, offsetY: clampedY } : s
      );
      onSlotsChange(updated);
    };

    const startHoldMove = (slotId: string, dirX: number, dirY: number, multiplier = 1) => {
      // 仅执行一次移动；不再支持按住连续移动
      moveOnce(slotId, dirX, dirY, multiplier);
    };

    // 计算画布像素尺寸（依据 ratio 与 canvasScale）
    const computeCanvasSize = useCallback(() => {
      const base = 800; // 初始显示基准（不再限制拖拽后的尺寸）
      const ratio = settings?.ratio || { w: 1, h: 1 };
      const rw = Math.max(1e-6, Number(ratio.w) || 1);
      const rh = Math.max(1e-6, Number(ratio.h) || 1);
      const scale = canvasScale != null ? canvasScale : base / Math.max(rw, rh);
      const width = Math.max(1, Math.round(scale * rw));
      const height = Math.max(1, Math.round(scale * rh));
      return { width, height };
    }, [settings?.ratio, canvasScale]);

    // 基于给定缩放（nextZoom）计算并钳制偏移，防止露底
    const clampOffsetsFor = useCallback((slot: PuzzleSlot, nextZoom: number) => {
      const size = computeCanvasSize();
      const padding = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
      const gap = settings?.gap ?? 0;
      const innerW = Math.max(1, size.width - (padding.left + padding.right));
      const innerH = Math.max(1, size.height - (padding.top + padding.bottom));
      const slotW = Math.max(0, slot.width * innerW - gap);
      const slotH = Math.max(0, slot.height * innerH - gap);

      const imgAspect = slot.image ? (slot.image.width / slot.image.height) : 1;
      const ang = ((slot.rotation % 360) + 360) % 360;
      const rotated = ang % 180 !== 0;
      const coverW = rotated ? slotH : slotW;
      const coverH = rotated ? slotW : slotH;
      let baseW: number, baseH: number;
      if (imgAspect > coverW / coverH) {
        baseH = coverH; baseW = baseH * imgAspect;
      } else {
        baseW = coverW; baseH = baseW / imgAspect;
      }
      const drawW = baseW * nextZoom;
      const drawH = baseH * nextZoom;
      const theta = (ang * Math.PI) / 180;
      const cosT = Math.abs(Math.cos(theta));
      const sinT = Math.abs(Math.sin(theta));
      const aabbW = drawW * cosT + drawH * sinT;
      const aabbH = drawW * sinT + drawH * cosT;
      const maxDXpx = Math.max(0, (aabbW - slotW) / 2);
      const maxDYpx = Math.max(0, (aabbH - slotH) / 2);
      const maxDXr = slotW > 0 ? maxDXpx / slotW : 0;
      const maxDYr = slotH > 0 ? maxDYpx / slotH : 0;

      const currX = slot.offsetX ?? 0;
      const currY = slot.offsetY ?? 0;
      const clampedX = Math.max(-maxDXr, Math.min(maxDXr, currX));
      const clampedY = Math.max(-maxDYr, Math.min(maxDYr, currY));
      return { offsetX: clampedX, offsetY: clampedY };
    }, [computeCanvasSize, settings?.gap, settings?.padding]);

    const drawRoundedRect = (ctx: CanvasRenderingContext2D, x: number, y: number, w: number, h: number, r: number) => {
      const radius = Math.max(0, Math.min(r, Math.min(w, h) / 2));
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.arcTo(x + w, y, x + w, y + h, radius);
      ctx.arcTo(x + w, y + h, x, y + h, radius);
      ctx.arcTo(x, y + h, x, y, radius);
      ctx.arcTo(x, y, x + w, y, radius);
      ctx.closePath();
    };

    // 支持分别控制四个角的圆角（仅对外边角做圆角）
    const drawRoundedRectCorners = (
      ctx: CanvasRenderingContext2D,
      x: number,
      y: number,
      w: number,
      h: number,
      rtl: number,
      rtr: number,
      rbr: number,
      rbl: number
    ) => {
      const half = Math.min(w, h) / 2;
      const tl = Math.max(0, Math.min(rtl, half));
      const tr = Math.max(0, Math.min(rtr, half));
      const br = Math.max(0, Math.min(rbr, half));
      const bl = Math.max(0, Math.min(rbl, half));

      ctx.beginPath();
      ctx.moveTo(x + tl, y);
      ctx.lineTo(x + w - tr, y);
      if (tr > 0) ctx.quadraticCurveTo(x + w, y, x + w, y + tr); else ctx.lineTo(x + w, y);
      ctx.lineTo(x + w, y + h - br);
      if (br > 0) ctx.quadraticCurveTo(x + w, y + h, x + w - br, y + h); else ctx.lineTo(x + w, y + h);
      ctx.lineTo(x + bl, y + h);
      if (bl > 0) ctx.quadraticCurveTo(x, y + h, x, y + h - bl); else ctx.lineTo(x, y + h);
      ctx.lineTo(x, y + tl);
      if (tl > 0) ctx.quadraticCurveTo(x, y, x + tl, y); else ctx.lineTo(x, y);
      ctx.closePath();
    };

    const drawPuzzle = useCallback(() => {
      const canvas = canvasRef.current;
      if (!canvas || !layout) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // 设置画布尺寸（由比例决定）
      const size = computeCanvasSize();
      canvas.width = size.width;
      canvas.height = size.height;
      // 通知父组件当前像素尺寸（仅在变化时）
      if (onCanvasSizeChange) {
        const last = lastReportedSizeRef.current;
        if (!last || last.width !== size.width || last.height !== size.height) {
          onCanvasSizeChange(size);
          lastReportedSizeRef.current = size;
        }
      }

      // 清空画布
      ctx.clearRect(0, 0, size.width, size.height);

      // 背景颜色
      const bgColor = settings?.backgroundColor ?? '#f8fafc';
      ctx.fillStyle = bgColor || '#f8fafc';
      ctx.fillRect(0, 0, size.width, size.height);

      // 背景图片（覆盖填充，居中裁剪）
      const bgImg = settings?.backgroundImage;
      if (bgImg) {
        const canvasAspect = size.width / size.height;
        const imgAspect = bgImg.width / bgImg.height;
        let dw = size.width, dh = size.height, dx = 0, dy = 0;
        if (imgAspect > canvasAspect) {
          dh = size.height;
          dw = dh * imgAspect;
          dx = -(dw - size.width) / 2;
          dy = 0;
        } else {
          dw = size.width;
          dh = dw / imgAspect;
          dx = 0;
          dy = -(dh - size.height) / 2;
        }
        ctx.drawImage(bgImg, dx, dy, dw, dh);
      }

      // 绘制每个槽位
      const padding = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
      const gap = settings?.gap ?? 0;
      const radius = settings?.borderRadius ?? 0;
      const cornerEnabled = Boolean(settings?.cornerRadiusEnabled);
      const corner = settings?.cornerRadius ?? { tl: 0, tr: 0, br: 0, bl: 0 };
      const roundAll = Boolean(settings?.roundAllSlots);
      const innerW = Math.max(1, size.width - (padding.left + padding.right));
      const innerH = Math.max(1, size.height - (padding.top + padding.bottom));

      slots.forEach((slot) => {
        let x = padding.left + slot.x * innerW;
        let y = padding.top + slot.y * innerH;
        let width = slot.width * innerW;
        let height = slot.height * innerH;

        // 应用间距（四周各缩进gap/2）
        const inset = gap / 2;
        x += inset;
        y += inset;
        width = Math.max(0, width - gap);
        height = Math.max(0, height - gap);

        // 仅对外部角应用圆角，内部分割角保持直角，避免 gap=0 时出现内部“空洞”
        const eps = 1e-4;
        const isLeftOuter = Math.abs(slot.x - 0) < eps;
        const isRightOuter = Math.abs(slot.x + slot.width - 1) < eps;
        const isTopOuter = Math.abs(slot.y - 0) < eps;
        const isBottomOuter = Math.abs(slot.y + slot.height - 1) < eps;
        // 当 roundAll 启用时，所有槽位的四角都应用圆角；否则仅外部角圆角
        const rTL = roundAll
          ? (cornerEnabled ? (corner.tl ?? 0) : radius)
          : (isLeftOuter && isTopOuter ? (cornerEnabled ? (corner.tl ?? 0) : radius) : 0);
        const rTR = roundAll
          ? (cornerEnabled ? (corner.tr ?? 0) : radius)
          : (isRightOuter && isTopOuter ? (cornerEnabled ? (corner.tr ?? 0) : radius) : 0);
        const rBR = roundAll
          ? (cornerEnabled ? (corner.br ?? 0) : radius)
          : (isRightOuter && isBottomOuter ? (cornerEnabled ? (corner.br ?? 0) : radius) : 0);
        const rBL = roundAll
          ? (cornerEnabled ? (corner.bl ?? 0) : radius)
          : (isLeftOuter && isBottomOuter ? (cornerEnabled ? (corner.bl ?? 0) : radius) : 0);

        if (slot.image) {
          // 绘制图片，支持缩放/旋转/水平翻转，并保证覆盖
          const imgAspect = slot.image.width / slot.image.height;
          const angle = ((slot.rotation % 360) + 360) % 360; // 0..359
          const rotated = angle % 180 !== 0;
          // 对于90/270度旋转，交换槽位宽高进行cover计算
          const coverW = rotated ? height : width;
          const coverH = rotated ? width : height;

          let baseW: number; // 覆盖所需的未缩放宽
          let baseH: number;
          if (imgAspect > coverW / coverH) {
            // 图片更宽，以高度为准
            baseH = coverH;
            baseW = coverH * imgAspect;
          } else {
            // 图片更高，以宽度为准
            baseW = coverW;
            baseH = coverW / imgAspect;
          }
          // 计算绘制缩放，支持“按槽位覆盖限制最小缩放”
          const minSetting = settings?.zoomMin ?? 0.1;
          const minZoom = (settings?.enforceMinZoomBySlotSize ? Math.max(1, minSetting) : minSetting);
          const zoom = Math.max(minZoom, slot.zoom || 1);
          const drawW = baseW * zoom;
          const drawH = baseH * zoom;

          // 槽位中心
          const cx = x + width / 2;
          const cy = y + height / 2;

          ctx.save();
          // 裁剪到槽位区域
          drawRoundedRectCorners(ctx, x, y, width, height, rTL, rTR, rBR, rBL);
          ctx.clip();

          // 以槽位中心为原点进行变换
          ctx.translate(cx, cy);
          // 应用平移（屏幕坐标下的偏移，按槽位尺寸比例计算）
          const offXR = typeof slot.offsetX === 'number' ? slot.offsetX : 0;
          const offYR = typeof slot.offsetY === 'number' ? slot.offsetY : 0;
          if (offXR !== 0 || offYR !== 0) {
            ctx.translate(offXR * width, offYR * height);
          }
          if (slot.flipH) ctx.scale(-1, 1);
          if (slot.flipV) ctx.scale(1, -1);
          if (angle !== 0) ctx.rotate((angle * Math.PI) / 180);

          // 在变换后坐标系中，以图片中心对齐(0,0)
          ctx.drawImage(
            slot.image,
            -drawW / 2,
            -drawH / 2,
            drawW,
            drawH
          );
          ctx.restore();
        } else {
          // 绘制空槽位
          ctx.fillStyle = '#e2e8f0';
          drawRoundedRectCorners(ctx, x, y, width, height, rTL, rTR, rBR, rBL);
          ctx.fill();

          // 绘制虚线边框
          ctx.strokeStyle = '#cbd5e1';
          ctx.setLineDash([5, 5]);
          ctx.lineWidth = 2;
          drawRoundedRectCorners(ctx, x, y, width, height, rTL, rTR, rBR, rBL);
          ctx.stroke();
          ctx.setLineDash([]);
        }

        // 注意：不再为所有槽位统一描边，避免在 gap=0 时导出出现视觉“缝隙”
      });
    }, [layout, slots, computeCanvasSize, settings?.padding, settings?.gap, settings?.borderRadius, settings?.cornerRadiusEnabled, settings?.cornerRadius, settings?.roundAllSlots, settings?.backgroundColor, settings?.backgroundImage, onCanvasSizeChange]);

    useEffect(() => {
      drawPuzzle();
    }, [drawPuzzle]);

    // Esc 清除选择
    useEffect(() => {
      if (!selectionEnabled || !onClearSelection) return;
      const onKey = (e: KeyboardEvent) => { if (e.key === 'Escape') onClearSelection(); };
      window.addEventListener('keydown', onKey);
      return () => window.removeEventListener('keydown', onKey);
    }, [selectionEnabled, onClearSelection]);

    const handleFileUpload = useCallback((slotId: string, file: File) => {
      const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
      const maxSize = 50 * 1024 * 1024; // 50MB

      if (!validTypes.includes(file.type)) {
        alert('不支持的文件格式。支持 JPG、PNG、WebP 和 GIF 格式。');
        return;
      }

      if (file.size > maxSize) {
        alert('文件大小超过 50MB 限制。');
        return;
      }

      onImageUpload(slotId, file);
    }, [onImageUpload]);

    const handleSlotClick = useCallback((slotId: string) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          handleFileUpload(slotId, file);
        }
      };
      input.click();
    }, [handleFileUpload]);

    // 交换两个槽位的图片
    const swapSlotImages = useCallback((srcId: string, tgtId: string) => {
      if (!onSlotsChange) return;
      if (srcId === tgtId) return;
      const src = slots.find((s) => s.id === srcId);
      const tgt = slots.find((s) => s.id === tgtId);
      if (!src || !tgt) return;
      const resetState = {
        zoom: 1,
        rotation: 0,
        flipH: false,
        flipV: false,
        offsetX: 0,
        offsetY: 0,
      } as const;
      const updated: PuzzleSlot[] = slots.map((s) => {
        if (s.id === srcId)
          return { ...s, image: tgt.image, ...resetState };
        if (s.id === tgtId)
          return { ...s, image: src.image, ...resetState };
        return s;
      });
      onSlotsChange(updated);
    }, [onSlotsChange, slots]);

    // 计算竖/横分割线的“可见段”，仅在实际存在槽位边界处显示
    const verticalSegments = useMemo(() => {
      const eps = 1e-4;
      const posSet = new Set<number>();
      slots.forEach((s) => {
        const left = Number(s.x.toFixed(4));
        const right = Number((s.x + s.width).toFixed(4));
        if (left > 0 && left < 1) posSet.add(left);
        if (right > 0 && right < 1) posSet.add(right);
      });
      const segments: { pos: number; top: number; height: number }[] = [];
      Array.from(posSet).sort((a, b) => a - b).forEach((pos) => {
        const ranges: Array<{ start: number; end: number }> = [];
        slots.forEach((s) => {
          const left = s.x;
          const right = s.x + s.width;
          if (Math.abs(left - pos) < eps || Math.abs(right - pos) < eps) {
            ranges.push({ start: s.y, end: s.y + s.height });
          }
        });
        // 合并真正“重叠”的区间；端点相接不合并，以保持上下段独立
        ranges.sort((a, b) => a.start - b.start);
        const merged: Array<{ start: number; end: number }> = [];
        for (const r of ranges) {
          if (merged.length === 0) {
            merged.push({ ...r });
            continue;
          }
          const last = merged[merged.length - 1];
          // 仅当 r.start 小于 last.end - eps 才认为有实质重叠，需要合并
          if (r.start <= last.end - eps) {
            last.end = Math.max(last.end, r.end);
          } else {
            merged.push({ ...r });
          }
        }
        merged.forEach((m) => {
          const h = Math.max(0, m.end - m.start);
          if (h > eps) segments.push({ pos, top: m.start, height: h });
        });
      });
      return segments;
    }, [slots]);

    // 横向分割线段（放在 map 之前，避免前置引用）
    const horizontalSegments = useMemo(() => {
      const eps = 1e-4;
      const posSet = new Set<number>();
      slots.forEach((s) => {
        const top = Number(s.y.toFixed(4));
        const bottom = Number((s.y + s.height).toFixed(4));
        if (top > 0 && top < 1) posSet.add(top);
        if (bottom > 0 && bottom < 1) posSet.add(bottom);
      });
      const segments: { pos: number; left: number; width: number }[] = [];
      Array.from(posSet).sort((a, b) => a - b).forEach((pos) => {
        const ranges: Array<{ start: number; end: number }> = [];
        slots.forEach((s) => {
          const top = s.y;
          const bottom = s.y + s.height;
          if (Math.abs(top - pos) < eps || Math.abs(bottom - pos) < eps) {
            ranges.push({ start: s.x, end: s.x + s.width });
          }
        });
        ranges.sort((a, b) => a.start - b.start);
        const merged: Array<{ start: number; end: number }> = [];
        for (const r of ranges) {
          if (merged.length === 0) {
            merged.push({ ...r });
            continue;
          }
          const last = merged[merged.length - 1];
          // 仅当有实质重叠才合并；端点相接不合并
          if (r.start <= last.end - eps) {
            last.end = Math.max(last.end, r.end);
          } else {
            merged.push({ ...r });
          }
        }
        merged.forEach((m) => {
          const w = Math.max(0, m.end - m.start);
          if (w > eps) segments.push({ pos, left: m.start, width: w });
        });
      });
      return segments;
    }, [slots]);

    // 将分割线段按位置聚合，方便命中测试
    const verticalSegMap = useMemo(() => {
      const m = new Map<number, Array<{ top: number; height: number }>>();
      verticalSegments.forEach((seg) => {
        const arr = m.get(seg.pos) || [];
        arr.push({ top: seg.top, height: seg.height });
        m.set(seg.pos, arr);
      });
      return m;
    }, [verticalSegments]);

    const horizontalSegMap = useMemo(() => {
      const m = new Map<number, Array<{ left: number; width: number }>>();
      horizontalSegments.forEach((seg) => {
        const arr = m.get(seg.pos) || [];
        arr.push({ left: seg.left, width: seg.width });
        m.set(seg.pos, arr);
      });
      return m;
    }, [horizontalSegments]);

    // 容器鼠标移动：检测是否靠近某条分割线
    const handleContainerMouseMove = useCallback((e: any) => {
      const container = containerRef.current;
      if (!container) return;
      const rect = container.getBoundingClientRect();
      // 优先检测靠近容器边/角（以容器矩形为准）
      if (!dragging) {
        const edgeTh = 12; // px，提高边缘命中阈值，便于触发 resize 指针
        const withinX = e.clientX >= rect.left - edgeTh && e.clientX <= rect.right + edgeTh;
        const withinY = e.clientY >= rect.top - edgeTh && e.clientY <= rect.bottom + edgeTh;
        const nearL = Math.abs(e.clientX - rect.left) <= edgeTh && withinY;
        const nearR = Math.abs(e.clientX - rect.right) <= edgeTh && withinY;
        const nearT = Math.abs(e.clientY - rect.top) <= edgeTh && withinX;
        const nearB = Math.abs(e.clientY - rect.bottom) <= edgeTh && withinX;

        let edge: typeof hoveredEdge = null;
        if (nearL && nearT) edge = 'corner-nw';
        else if (nearR && nearT) edge = 'corner-ne';
        else if (nearR && nearB) edge = 'corner-se';
        else if (nearL && nearB) edge = 'corner-sw';
        else if (nearL) edge = 'left';
        else if (nearR) edge = 'right';
        else if (nearT) edge = 'top';
        else if (nearB) edge = 'bottom';

        if (edge) {
          if (hoveredEdge !== edge) setHoveredEdge(edge);
          if (hoveredDivider) setHoveredDivider(null);
          return;
        } else if (!ratioDragging) {
          if (hoveredEdge !== null) setHoveredEdge(null);
        }
      }
      const pad = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
      const size = computeCanvasSize();
      const innerW = Math.max(1, size.width - (pad.left + pad.right));
      const innerH = Math.max(1, size.height - (pad.top + pad.bottom));
      const innerWpx = rect.width * (innerW / size.width);
      const innerHpx = rect.height * (innerH / size.height);

      // 归一化坐标（相对 inner 区域 0-1）
      const relX = (e.clientX - rect.left - pad.left * (rect.width / size.width)) / (rect.width * (innerW / size.width));
      const relY = (e.clientY - rect.top - pad.top * (rect.height / size.height)) / (rect.height * (innerH / size.height));

      // 距离阈值（像素 -> 归一化）
      const pxThreshold = 10;
      const thX = pxThreshold / Math.max(1, innerWpx);
      const thY = pxThreshold / Math.max(1, innerHpx);

      // 拖拽或比例调整进行中：暂停 hover 命中，避免高频状态变化引起闪烁
      if (dragging || ratioDragging) {
        if (dragging && hoveredDivider) setHoveredDivider(null);
        return;
      }

      if (settings?.independentDividerDrag) {
        // 命中测试：返回具体的“线段”，而不是仅位置
        let best: null |
          ({ type: 'v'; pos: number; top: number; height: number } |
           { type: 'h'; pos: number; left: number; width: number }) = null;
        let bestDist = Infinity;

        verticalSegments.forEach((seg) => {
          const dx = Math.abs(relX - seg.pos);
          if (dx <= thX) {
            const withinY = relY >= seg.top - thY && relY <= seg.top + seg.height + thY;
            if (withinY && dx < bestDist) {
              best = { type: 'v', pos: seg.pos, top: seg.top, height: seg.height };
              bestDist = dx;
            }
          }
        });
        horizontalSegments.forEach((seg) => {
          const dy = Math.abs(relY - seg.pos);
          if (dy <= thY) {
            const withinX = relX >= seg.left - thX && relX <= seg.left + seg.width + thX;
            if (withinX && dy < bestDist) {
              best = { type: 'h', pos: seg.pos, left: seg.left, width: seg.width };
              bestDist = dy;
            }
          }
        });
        if (best) {
          setHoveredDivider((prev) => {
            const same = !!prev && prev.type === best!.type && Math.abs(prev.pos - best!.pos) < 1e-6
              && (prev.type === 'v'
                    ? ('top' in prev ? Math.abs((prev.top + prev.height) - (best as any).top - (best as any).height) < 1e-6 && Math.abs(prev.top - (best as any).top) < 1e-6 : !('top' in best!))
                    : ('left' in prev ? Math.abs((prev.left + prev.width) - (best as any).left - (best as any).width) < 1e-6 && Math.abs(prev.left - (best as any).left) < 1e-6 : !('left' in best!))
                );
            return same ? prev : best;
          });
        } else {
          // 粘滞阈值：若已存在 hover，且仍在较宽阈值内，则保留，避免闪烁
          const stick = 1.5;
          setHoveredDivider((prev) => {
            if (!prev) return null;
            if (prev.type === 'v') {
              const dx = Math.abs(relX - prev.pos);
              // 在该位置的任一 segment 的 Y 范围内
              const ranges = verticalSegments.filter(s => Math.abs(s.pos - prev.pos) < 1e-6);
              const inY = ranges.some(r => relY >= r.top - stick*thY && relY <= r.top + r.height + stick*thY);
              return (dx <= stick*thX && inY) ? prev : null;
            } else {
              const dy = Math.abs(relY - prev.pos);
              const ranges = horizontalSegments.filter(s => Math.abs(s.pos - prev.pos) < 1e-6);
              const inX = ranges.some(r => relX >= r.left - stick*thX && relX <= r.left + r.width + stick*thX);
              return (dy <= stick*thY && inX) ? prev : null;
            }
          });
        }
      } else {
        // 原始：仅按位置合并判断整条分割线
        let bestType: 'v' | 'h' | null = null;
        let bestPos = 0;
        let bestDist = Infinity;

        verticalSegMap.forEach((ranges, pos) => {
          const dx = Math.abs(relX - pos);
          if (dx <= thX) {
            const hit = ranges.some((r) => relY >= r.top - thY && relY <= r.top + r.height + thY);
            if (hit && dx < bestDist) {
              bestType = 'v';
              bestPos = pos;
              bestDist = dx;
            }
          }
        });
        horizontalSegMap.forEach((ranges, pos) => {
          const dy = Math.abs(relY - pos);
          if (dy <= thY) {
            const hit = ranges.some((r) => relX >= r.left - thX && relX <= r.left + r.width + thX);
            if (hit && dy < bestDist) {
              bestType = 'h';
              bestPos = pos;
              bestDist = dy;
            }
          }
        });
        if (bestType) {
          const next = { type: bestType, pos: bestPos } as const;
          setHoveredDivider((prev) => {
            const same = !!prev && prev.type === next.type && Math.abs(prev.pos - next.pos) < 1e-6;
            return same ? prev : next as any;
          });
        } else {
          const stick = 1.5;
          setHoveredDivider((prev) => {
            if (!prev) return null;
            if (prev.type === 'v') {
              const dx = Math.abs(relX - prev.pos);
              const ranges = verticalSegMap.get(prev.pos) || [];
              const inY = ranges.some((r: any) => relY >= r.top - stick*thY && relY <= r.top + r.height + stick*thY);
              return (dx <= stick*thX && inY) ? prev : null;
            } else {
              const dy = Math.abs(relY - prev.pos);
              const ranges = horizontalSegMap.get(prev.pos) || [];
              const inX = ranges.some((r: any) => relX >= r.left - stick*thX && relX <= r.left + r.width + stick*thX);
              return (dy <= stick*thY && inX) ? prev : null;
            }
          });
        }
      }
    }, [computeCanvasSize, dragging, horizontalSegments, settings?.padding, verticalSegments, ratioDragging, settings?.independentDividerDrag, verticalSegMap, horizontalSegMap]);

    // 拖拽开始时清空 hover，避免渲染层在 hover/drag 状态之间抖动
    useEffect(() => {
      if (dragging) setHoveredDivider(null);
    }, [dragging]);

    // 容器鼠标离开：隐藏
    const handleContainerMouseLeave = useCallback(() => {
      if (!dragging) setHoveredDivider(null);
      if (!ratioDragging) setHoveredEdge(null);
    }, [dragging, ratioDragging]);

    // handleContainerMouseDown 将在 beginDrag* 之后定义

    // horizontalSegments 已在上方定义

    // —— 槽位内拖拽平移（pan）——
    const beginPan = useCallback((slot: PuzzleSlot, startX: number, startY: number, slotRect: DOMRect) => {
      if (!slot.image || !onSlotsChange) return;
      // 计算最大偏移（与 moveOnce 保持一致）
      const size = computeCanvasSize();
      const padding = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
      const gap = settings?.gap ?? 0;
      const innerW = Math.max(1, size.width - (padding.left + padding.right));
      const innerH = Math.max(1, size.height - (padding.top + padding.bottom));
      const slotW = Math.max(0, slot.width * innerW - gap);
      const slotH = Math.max(0, slot.height * innerH - gap);

      const imgAspect = slot.image.width / slot.image.height;
      const ang = ((slot.rotation % 360) + 360) % 360;
      const rotated = ang % 180 !== 0;
      const coverW = rotated ? slotH : slotW;
      const coverH = rotated ? slotW : slotH;
      let baseW: number, baseH: number;
      if (imgAspect > coverW / coverH) {
        baseH = coverH; baseW = baseH * imgAspect;
      } else {
        baseW = coverW; baseH = baseW / imgAspect;
      }
      const minSetting = settings?.zoomMin ?? 0.1;
      const minZoom = settings?.enforceMinZoomBySlotSize ? Math.max(1, minSetting) : minSetting;
      const zoom = Math.max(minZoom, slot.zoom || 1);
      const drawW = baseW * zoom;
      const drawH = baseH * zoom;
      const theta = (ang * Math.PI) / 180;
      const cosT = Math.abs(Math.cos(theta));
      const sinT = Math.abs(Math.sin(theta));
      const aabbW = drawW * cosT + drawH * sinT;
      const aabbH = drawW * sinT + drawH * cosT;
      const maxDXpx = Math.max(0, (aabbW - slotW) / 2);
      const maxDYpx = Math.max(0, (aabbH - slotH) / 2);
      const maxDXr = slotW > 0 ? maxDXpx / slotW : 0;
      const maxDYr = slotH > 0 ? maxDYpx / slotH : 0;

      panRef.current = {
        slotId: slot.id,
        startX,
        startY,
        baseOffX: slot.offsetX ?? 0,
        baseOffY: slot.offsetY ?? 0,
        slotWpx: Math.max(1, slotRect.width),
        slotHpx: Math.max(1, slotRect.height),
        maxDXr,
        maxDYr,
      };
      setIsPanning(true);
      suppressClickRef.current = true; // 拖拽期间抑制点击上传

      const onMove = (clientX: number, clientY: number) => {
        const p = panRef.current; if (!p) return;
        const dxr = (clientX - p.startX) / p.slotWpx;
        const dyr = (clientY - p.startY) / p.slotHpx;
        const nxRaw = p.baseOffX + dxr;
        const nyRaw = p.baseOffY + dyr;
        const clamp = Boolean(settings?.enforceMinZoomBySlotSize);
        const nx = clamp ? Math.max(-p.maxDXr, Math.min(p.maxDXr, nxRaw)) : nxRaw;
        const ny = clamp ? Math.max(-p.maxDYr, Math.min(p.maxDYr, nyRaw)) : nyRaw;
        onSlotsChange(
          slotsRef.current.map((s) => s.id === p.slotId ? { ...s, offsetX: nx, offsetY: ny } : s)
        );
      };

      const handleMouseMove = (e: MouseEvent) => { e.preventDefault(); onMove(e.clientX, e.clientY); };
      const handleTouchMove = (e: TouchEvent) => {
        if (e.touches && e.touches[0]) { e.preventDefault(); onMove(e.touches[0].clientX, e.touches[0].clientY); }
      };
      const endPan = () => {
        setIsPanning(false);
        panRef.current = null;
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', endPan);
        window.removeEventListener('touchmove', handleTouchMove);
        window.removeEventListener('touchend', endPan);
        // 短暂延迟后恢复点击
        if (suppressTimerRef.current !== null) {
          window.clearTimeout(suppressTimerRef.current);
          suppressTimerRef.current = null;
        }
        suppressTimerRef.current = window.setTimeout(() => {
          suppressClickRef.current = false;
          suppressTimerRef.current = null;
        }, 150);
      };

      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', endPan);
      window.addEventListener('touchmove', handleTouchMove, { passive: false });
      window.addEventListener('touchend', endPan);
    }, [computeCanvasSize, onSlotsChange, settings?.gap, settings?.padding]);

    const beginDragVertical = useCallback((pos: number, segTop?: number, segBottom?: number) => {
      const minWidth = 0.05; // 防止折叠
      // 计算左右约束
      let lower = 0 + minWidth;
      let upper = 1 - minWidth;
      slots.forEach((s) => {
        const left = s.x;
        const right = s.x + s.width;
        // 线段独立时，仅考虑与目标线段在垂直方向有交叠的槽位；否则整条
        if (settings?.independentDividerDrag && typeof segTop === 'number' && typeof segBottom === 'number') {
          const top = s.y;
          const bottom = s.y + s.height;
          const overlap = !(bottom <= segTop + 1e-4 || top >= segBottom - 1e-4);
          if (!overlap) return;
        }
        // 该槽与分割线接触则产生约束
        if (Math.abs(right - pos) < 1e-4) {
          lower = Math.max(lower, left + minWidth);
        }
        if (Math.abs(left - pos) < 1e-4) {
          upper = Math.min(upper, right - minWidth);
        }
      });
      if (settings?.independentDividerDrag && typeof segTop === 'number' && typeof segBottom === 'number') {
        setDragging({ type: 'v', pos, lowerBound: lower, upperBound: upper, segTop, segBottom });
      } else {
        setDragging({ type: 'v', pos, lowerBound: lower, upperBound: upper });
      }
    }, [slots, settings?.independentDividerDrag]);

    const beginDragHorizontal = useCallback((pos: number, segLeft?: number, segRight?: number) => {
      const minHeight = 0.05;
      let lower = 0 + minHeight;
      let upper = 1 - minHeight;
      slots.forEach((s) => {
        const top = s.y;
        const bottom = s.y + s.height;
        // 线段独立时，仅考虑与目标线段在水平方向有交叠的槽位；否则整条
        if (settings?.independentDividerDrag && typeof segLeft === 'number' && typeof segRight === 'number') {
          const left = s.x;
          const right = s.x + s.width;
          const overlap = !(right <= segLeft + 1e-4 || left >= segRight - 1e-4);
          if (!overlap) return;
        }
        if (Math.abs(bottom - pos) < 1e-4) {
          lower = Math.max(lower, top + minHeight);
        }
        if (Math.abs(top - pos) < 1e-4) {
          upper = Math.min(upper, bottom - minHeight);
        }
      });
      if (settings?.independentDividerDrag && typeof segLeft === 'number' && typeof segRight === 'number') {
        setDragging({ type: 'h', pos, lowerBound: lower, upperBound: upper, segLeft, segRight });
      } else {
        setDragging({ type: 'h', pos, lowerBound: lower, upperBound: upper });
      }
    }, [slots, settings?.independentDividerDrag]);

    // 容器鼠标按下：若靠近某分割线则开始拖动（放在 beginDrag* 之后，避免 TDZ）
    const beginRatioDrag = useCallback((mode: NonNullable<typeof hoveredEdge>, startX: number, startY: number) => {
      const container = containerRef.current; if (!container) return;
      const size = computeCanvasSize();
      const rect = container.getBoundingClientRect();
      const initScaleX = Math.max(1e-6, size.width / Math.max(1, rect.width));
      const initScaleY = Math.max(1e-6, size.height / Math.max(1, rect.height));
      // 估算容器是否被父元素居中，若居中则边缘移动相对鼠标为 1/2，需要在尺寸增量上做 2x 补偿
      const parent = container.parentElement as HTMLElement | null;
      let centeredX = false, centeredY = false;
      if (parent) {
        const pr = parent.getBoundingClientRect();
        const expectedLeft = pr.left + (pr.width - rect.width) / 2;
        const expectedTop = pr.top + (pr.height - rect.height) / 2;
        centeredX = Math.abs(rect.left - expectedLeft) < 2; // 允许少量像素误差
        centeredY = Math.abs(rect.top - expectedTop) < 2;
      }
      setRatioDragging({
        mode,
        startX,
        startY,
        initWpx: size.width,
        initHpx: size.height,
        initScaleX,
        initScaleY,
        initRatioW: settings?.ratio?.w ?? 1,
        initRatioH: settings?.ratio?.h ?? 1,
        centeredX,
        centeredY,
      });
      // 拖拽期间抑制点击
      suppressClickRef.current = true;
    }, [computeCanvasSize, settings?.ratio?.h, settings?.ratio?.w]);

    const handleContainerMouseDown = useCallback((e: any) => {
      if (hoveredEdge) {
        e.preventDefault();
        e.stopPropagation();
        beginRatioDrag(hoveredEdge, e.clientX, e.clientY);
        return;
      }
      if (!hoveredDivider) return;
      e.preventDefault();
      e.stopPropagation();
      if (hoveredDivider.type === 'v') {
        if ('top' in hoveredDivider) beginDragVertical(hoveredDivider.pos, hoveredDivider.top, hoveredDivider.top + hoveredDivider.height);
        else beginDragVertical(hoveredDivider.pos);
      } else {
        if ('left' in hoveredDivider) beginDragHorizontal(hoveredDivider.pos, hoveredDivider.left, hoveredDivider.left + hoveredDivider.width);
        else beginDragHorizontal(hoveredDivider.pos);
      }
    }, [beginDragHorizontal, beginDragVertical, hoveredDivider, hoveredEdge, beginRatioDrag]);

    // 用于分割线拖拽的 rAF 合帧，减少闪烁
    const dragRAFRef = useRef<number | null>(null);
    const dragPendingRef = useRef<{ x: number; y: number } | null>(null);
    const lastEmittedPosRef = useRef<number | null>(null);

    // 拖动处理
    useEffect(() => {
      if (!dragging) return;
      const container = containerRef.current;
      if (!container) return;
      // 清空上次状态
      lastEmittedPosRef.current = dragging.pos;
      if (dragRAFRef.current) {
        cancelAnimationFrame(dragRAFRef.current);
        dragRAFRef.current = null;
      }

      const emitMove = (clientX: number, clientY: number) => {
        if (!onSlotsChange) return;
        const rect = container.getBoundingClientRect();
        let newPos: number;
        const pad = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
        const size = computeCanvasSize();
        const innerW = Math.max(1, size.width - (pad.left + pad.right));
        const innerH = Math.max(1, size.height - (pad.top + pad.bottom));
        const EPS = 1e-4; // 最小位移阈值
        if (dragging.type === 'v') {
          const relX = (clientX - rect.left - pad.left * (rect.width / size.width)) / (rect.width * (innerW / size.width));
          // 实时复算约束，防止在拖动中越界穿过覆盖区域
          const minWidth = 0.05;
          let liveLower = 0 + minWidth;
          let liveUpper = 1 - minWidth;
          const useSeg = 'segTop' in dragging && 'segBottom' in dragging;
          slots.forEach((s) => {
            const left = s.x;
            const right = s.x + s.width;
            if (useSeg) {
              const top = s.y;
              const bottom = s.y + s.height;
              const overlap = !(bottom <= dragging.segTop + 1e-4 || top >= dragging.segBottom - 1e-4);
              if (!overlap) return;
            }
            if (Math.abs(right - dragging.pos) < 1e-4) {
              liveLower = Math.max(liveLower, left + minWidth);
            }
            if (Math.abs(left - dragging.pos) < 1e-4) {
              liveUpper = Math.min(liveUpper, right - minWidth);
            }
          });
          newPos = Math.min(liveUpper, Math.max(liveLower, relX));
          if (lastEmittedPosRef.current !== null && Math.abs(newPos - lastEmittedPosRef.current) < EPS) return; // 无显著变化不触发
          const updated: PuzzleSlot[] = slots.map((s) => {
            const left = s.x;
            const right = s.x + s.width;
            // 独立模式：仅更新线段覆盖范围；否则整条分割线
            if ('segTop' in dragging) {
              const top = s.y;
              const bottom = s.y + s.height;
              const overlap = !(bottom <= dragging.segTop + 1e-4 || top >= dragging.segBottom - 1e-4);
              if (!overlap) return s;
            }
            if (Math.abs(right - dragging.pos) < 1e-4) {
              return { ...s, width: Math.max(0, newPos - left) };
            }
            if (Math.abs(left - dragging.pos) < 1e-4) {
              const newX = newPos;
              return { ...s, x: newX, width: Math.max(0, right - newX) };
            }
            return s;
          });
          onSlotsChange(updated);
          lastEmittedPosRef.current = newPos;
          // 避免频繁 setState 抖动，仅在 rAF 内同步 dragging.pos
          setDragging((prev) => (prev ? { ...prev, pos: newPos } : prev));
        } else {
          const relY = (clientY - rect.top - pad.top * (rect.height / size.height)) / (rect.height * (innerH / size.height));
          const minHeight = 0.05;
          let liveLower = 0 + minHeight;
          let liveUpper = 1 - minHeight;
          const useSeg = 'segLeft' in dragging && 'segRight' in dragging;
          slots.forEach((s) => {
            const top = s.y;
            const bottom = s.y + s.height;
            if (useSeg) {
              const left = s.x;
              const right = s.x + s.width;
              const overlap = !(right <= dragging.segLeft + 1e-4 || left >= dragging.segRight - 1e-4);
              if (!overlap) return;
            }
            if (Math.abs(bottom - dragging.pos) < 1e-4) {
              liveLower = Math.max(liveLower, top + minHeight);
            }
            if (Math.abs(top - dragging.pos) < 1e-4) {
              liveUpper = Math.min(liveUpper, bottom - minHeight);
            }
          });
          newPos = Math.min(liveUpper, Math.max(liveLower, relY));
          if (lastEmittedPosRef.current !== null && Math.abs(newPos - lastEmittedPosRef.current) < EPS) return;
          const updated: PuzzleSlot[] = slots.map((s) => {
            const top = s.y;
            const bottom = s.y + s.height;
            // 独立模式：仅更新线段覆盖范围；否则整条分割线
            if ('segLeft' in dragging) {
              const left = s.x;
              const right = s.x + s.width;
              const overlap = !(right <= dragging.segLeft + 1e-4 || left >= dragging.segRight - 1e-4);
              if (!overlap) return s;
            }
            if (Math.abs(bottom - dragging.pos) < 1e-4) {
              return { ...s, height: Math.max(0, newPos - top) };
            }
            if (Math.abs(top - dragging.pos) < 1e-4) {
              const newY = newPos;
              return { ...s, y: newY, height: Math.max(0, bottom - newY) };
            }
            return s;
          });
          onSlotsChange(updated);
          lastEmittedPosRef.current = newPos;
          setDragging((prev) => (prev ? { ...prev, pos: newPos } : prev));
        }
      };

      const processRAF = () => {
        dragRAFRef.current = null;
        const p = dragPendingRef.current;
        dragPendingRef.current = null;
        if (p) emitMove(p.x, p.y);
      };

      const onMove = (e: MouseEvent) => {
        dragPendingRef.current = { x: e.clientX, y: e.clientY };
        if (dragRAFRef.current == null) {
          dragRAFRef.current = requestAnimationFrame(processRAF);
        }
      };

      const onUp = () => {
        setDragging(null);
        dragPendingRef.current = null;
        if (dragRAFRef.current) {
          cancelAnimationFrame(dragRAFRef.current);
          dragRAFRef.current = null;
        }
        // 在分割条拖拽结束后的极短时间内抑制点击，避免触发槽位的上传点击
        suppressClickRef.current = true;
        if (suppressTimerRef.current !== null) {
          window.clearTimeout(suppressTimerRef.current);
          suppressTimerRef.current = null;
        }
        suppressTimerRef.current = window.setTimeout(() => {
          suppressClickRef.current = false;
          suppressTimerRef.current = null;
        }, 200);
      };

      window.addEventListener('mousemove', onMove, { passive: true });
      window.addEventListener('mouseup', onUp);
      return () => {
        window.removeEventListener('mousemove', onMove);
        window.removeEventListener('mouseup', onUp);
        dragPendingRef.current = null;
        if (dragRAFRef.current) {
          cancelAnimationFrame(dragRAFRef.current);
          dragRAFRef.current = null;
        }
      };
    }, [dragging, onSlotsChange, slots, computeCanvasSize, settings?.padding]);

    // 画布比例拖拽处理
    useEffect(() => {
      if (!ratioDragging) return;
      const onMove = (e: MouseEvent) => {
        const dxCanvas = (e.clientX - ratioDragging.startX) * ratioDragging.initScaleX;
        const dyCanvas = (e.clientY - ratioDragging.startY) * ratioDragging.initScaleY;

        let newW = ratioDragging.initWpx;
        let newH = ratioDragging.initHpx;
        const mode = ratioDragging.mode;
        const fx = ratioDragging.centeredX ? 2 : 1;
        const fy = ratioDragging.centeredY ? 2 : 1;
        if (mode === 'left' || mode === 'corner-nw' || mode === 'corner-sw') newW = ratioDragging.initWpx - fx * dxCanvas;
        if (mode === 'right' || mode === 'corner-ne' || mode === 'corner-se') newW = ratioDragging.initWpx + fx * dxCanvas;
        if (mode === 'top' || mode === 'corner-nw' || mode === 'corner-ne') newH = ratioDragging.initHpx - fy * dyCanvas;
        if (mode === 'bottom' || mode === 'corner-sw' || mode === 'corner-se') newH = ratioDragging.initHpx + fy * dyCanvas;

        // 最小尺寸防止反向/过小
        const MIN_CANVAS_PX = 40;
        newW = Math.max(MIN_CANVAS_PX, newW);
        newH = Math.max(MIN_CANVAS_PX, newH);

        // 最大尺寸限制：
        // - 最大宽度为容器父元素的可用宽度（等同于视口宽度减去左右侧栏/边距）
        // - 最大高度为屏幕高度
        const container = containerRef.current;
        let maxWpx = Number.POSITIVE_INFINITY;
        let maxHpx = Number.POSITIVE_INFINITY;
        if (container) {
          const parent = container.parentElement as HTMLElement | null;
          const pr = parent?.getBoundingClientRect();
          if (pr) {
            maxWpx = pr.width;
          } else {
            maxWpx = window.innerWidth;
          }
          maxHpx = window.innerHeight;
        } else {
          maxWpx = window.innerWidth;
          maxHpx = window.innerHeight;
        }
        // 夹取到上限，避免超出可见区域
        newW = Math.min(newW, Math.max(MIN_CANVAS_PX, Math.floor(maxWpx)));
        newH = Math.min(newH, Math.max(MIN_CANVAS_PX, Math.floor(maxHpx)));

        const aspect = newW / newH; // w:h
        const lockAspect = Boolean(settings?.lockAspectOnResize);
        if (lockAspect) {
          // 锁定模式：以初始宽高为基准，进行等比统一缩放，避免主轴切换导致的跳跃
          const initW = Math.max(1e-6, ratioDragging.initWpx);
          const initH = Math.max(1e-6, ratioDragging.initHpx);
          // 拖拽产生的候选缩放因子（相对初始）
          const scaleW = newW / initW;
          const scaleH = newH / initH;
          let scale = 1;
          if (mode === 'left' || mode === 'right') {
            scale = scaleW;
          } else if (mode === 'top' || mode === 'bottom') {
            scale = scaleH;
          } else {
            // 角：选择变化更大的维度对应的缩放，保持连续手感
            scale = Math.abs(scaleW) >= Math.abs(scaleH) ? scaleW : scaleH;
          }

          // 依据可视限制夹取 scale（而不是分别夹取宽高）
          const container2 = containerRef.current;
          let maxW2 = Number.POSITIVE_INFINITY, maxH2 = Number.POSITIVE_INFINITY;
          if (container2) {
            const parent = container2.parentElement as HTMLElement | null;
            const pr = parent?.getBoundingClientRect();
            maxW2 = pr ? pr.width : window.innerWidth;
            maxH2 = window.innerHeight;
          } else {
            maxW2 = window.innerWidth; maxH2 = window.innerHeight;
          }
          const MIN_CANVAS_PX2 = 40;
          const maxScaleByW = Math.max(MIN_CANVAS_PX2 / initW, Math.floor(maxW2) / initW);
          const maxScaleByH = Math.max(MIN_CANVAS_PX2 / initH, Math.floor(maxH2) / initH);
          const maxScale = Math.min(maxScaleByW, maxScaleByH);
          const minScale = Math.max(MIN_CANVAS_PX2 / initW, MIN_CANVAS_PX2 / initH);
          scale = Math.min(Math.max(scale, minScale), maxScale);

          // 用统一的 scale 回推像素宽高，保证比例恒定
          newW = initW * scale;
          newH = initH * scale;

          // 更新像素尺度，不改变 ratio
          const baseScale = Math.max(1e-6, ratioDragging.initWpx / Math.max(1e-6, ratioDragging.initRatioW));
          setCanvasScale(baseScale * scale);
        } else {
          // 非锁定：根据新的长宽比回写 ratio，并同步像素尺度
          let nextRatioW = ratioDragging.initRatioW;
          let nextRatioH = ratioDragging.initRatioH;
          if (mode === 'top' || mode === 'bottom') {
            nextRatioW = Math.max(1, ratioDragging.initRatioW);
            nextRatioH = Math.max(1, nextRatioW / Math.max(1e-6, aspect));
          } else {
            nextRatioH = Math.max(1, ratioDragging.initRatioH);
            nextRatioW = Math.max(1, aspect * nextRatioH);
          }

          if (onRatioChange) {
            onRatioChange({ w: nextRatioW, h: nextRatioH });
          }

          setCanvasScale(() => {
            if (mode === 'top' || mode === 'bottom') {
              return newW / nextRatioW;
            } else {
              return newH / nextRatioH;
            }
          });
        }
      };

      const onUp = () => {
        setRatioDragging(null);
        setHoveredEdge(null);
        // 拖拽结束后短暂抑制点击，避免触发槽位点击上传
        if (suppressTimerRef.current !== null) {
          window.clearTimeout(suppressTimerRef.current);
          suppressTimerRef.current = null;
        }
        suppressTimerRef.current = window.setTimeout(() => {
          suppressClickRef.current = false;
          suppressTimerRef.current = null;
        }, 200);
      };

      window.addEventListener('mousemove', onMove, { passive: true });
      window.addEventListener('mouseup', onUp);
      return () => {
        window.removeEventListener('mousemove', onMove);
        window.removeEventListener('mouseup', onUp);
      };
    }, [ratioDragging, onRatioChange]);

    if (!layout) {
      return (
        <div className={`flex items-center justify-center w-96 h-96 bg-gray-100 dark:bg-gray-800 rounded-2xl border-2 border-dashed border-gray-300 dark:border-gray-600 ${className}`}>
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <Plus className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">
              请先选择拼图布局
            </p>
          </div>
        </div>
      );
    }

    return (
      <div
        ref={containerRef}
        className={`relative ${className} ${
          dragging?.type === 'v' || hoveredDivider?.type === 'v' ? 'cursor-col-resize' : ''
        } ${dragging?.type === 'h' || hoveredDivider?.type === 'h' ? 'cursor-row-resize' : ''}`}
        style={{
          width: `${computeCanvasSize().width}px`,
          height: `${computeCanvasSize().height}px`,
          cursor:
            ((): string | undefined => {
              const m = ratioDragging?.mode || hoveredEdge;
              if (!m) return undefined;
              if (m === 'left' || m === 'right') return 'ew-resize';
              if (m === 'top' || m === 'bottom') return 'ns-resize';
              if (m === 'corner-nw' || m === 'corner-se') return 'nwse-resize';
              if (m === 'corner-ne' || m === 'corner-sw') return 'nesw-resize';
              return undefined;
            })(),
        }}
        onMouseMove={handleContainerMouseMove}
        onMouseLeave={handleContainerMouseLeave}
        onMouseDown={handleContainerMouseDown}
        onClickCapture={(e) => {
          if (suppressClickRef.current) {
            e.preventDefault();
            e.stopPropagation();
          }
        }}
      >
        <canvas
          ref={canvasRef}
          className="border border-gray-200 dark:border-gray-700 shadow-lg bg-white"
          style={{ width: '100%', height: '100%' }}
        />
        
        {/* 交互层 - 用于处理点击上传与每槽位控制（考虑 padding 与 gap 映射到百分比）*/}
        <div className="absolute inset-0 pointer-events-none z-10">
          {(() => {
            const size = computeCanvasSize();
            const pad = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
            const gapPx = settings?.gap ?? 0;
            const innerW = Math.max(1, size.width - (pad.left + pad.right));
            const innerH = Math.max(1, size.height - (pad.top + pad.bottom));
            const padLeftPct = (pad.left / size.width) * 100;
            const padTopPct = (pad.top / size.height) * 100;
            const innerWPct = (innerW / size.width) * 100;
            const innerHPct = (innerH / size.height) * 100;
            const gapXPct = (gapPx / size.width) * 100;
            const gapYPct = (gapPx / size.height) * 100;

            return slots.map((slot) => {
              const leftPct = padLeftPct + slot.x * innerWPct + gapXPct / 2;
              const topPct = padTopPct + slot.y * innerHPct + gapYPct / 2;
              const widthPct = Math.max(0, slot.width * innerWPct - gapXPct);
              const heightPct = Math.max(0, slot.height * innerHPct - gapYPct);
              const isSelected = selectionEnabled && selectedSlotIds?.includes(slot.id);
              return (
                <div
                  key={slot.id}
                  className={`absolute pointer-events-auto group ${
                    (dndTargetId === slot.id && dndSourceId !== slot.id) ? 'ring-2 ring-blue-500 rounded' : ''
                  } ${
                    isSelected ? 'ring-2 ring-emerald-500 rounded' : ''
                  } ${
                    dragging?.type === 'v' || hoveredDivider?.type === 'v'
                      ? 'cursor-col-resize'
                      : dragging?.type === 'h' || hoveredDivider?.type === 'h'
                      ? 'cursor-row-resize'
                      : selectionEnabled
                      ? 'cursor-pointer'
                      : slot.image
                      ? (isPanning && panRef.current?.slotId === slot.id ? 'cursor-grabbing' : 'cursor-grab')
                      : 'cursor-pointer'
                  }`}
                  style={{
                    left: `${leftPct}%`,
                    top: `${topPct}%`,
                    width: `${widthPct}%`,
                    height: `${heightPct}%`
                  }}
                  onMouseDown={(e) => {
                    const target = e.target as HTMLElement;
                    if (target?.closest('[data-control-panel="true"]')) {
                      e.preventDefault();
                      e.stopPropagation();
                      slotClickStartRef.current = null;
                      return;
                    }
                    // 仅当直接点击槽位容器背景时标记
                    slotClickStartRef.current = e.currentTarget === e.target ? slot.id : null;
                    // 靠近分割线时由分割线拖拽接管
                    if (hoveredDivider || hoveredEdge) return;
                    if (selectionEnabled) {
                      // 选择模式下不触发展示内平移
                      return;
                    }
                    // 桌面：按住 Alt/Option 键进行平移；否则保留默认拖拽换图
                    if (slot.image && e.altKey) {
                      e.preventDefault();
                      e.stopPropagation();
                      const rect = (e.currentTarget as HTMLDivElement).getBoundingClientRect();
                      beginPan(slot, e.clientX, e.clientY, rect);
                      return;
                    }
                  }}
                  onTouchStart={(e) => {
                    const target = e.target as HTMLElement;
                    if (target?.closest('[data-control-panel="true"]')) {
                      e.preventDefault();
                      e.stopPropagation();
                      slotClickStartRef.current = null;
                      return;
                    }
                    // 触控下同样支持平移手势
                    slotClickStartRef.current = e.currentTarget === e.target ? slot.id : null;
                    if (hoveredDivider) return;
                    if (!selectionEnabled && slot.image && e.touches && e.touches[0]) {
                      const t = e.touches[0];
                      e.preventDefault();
                      e.stopPropagation();
                      const rect = (e.currentTarget as HTMLDivElement).getBoundingClientRect();
                      beginPan(slot, t.clientX, t.clientY, rect);
                    }
                  }}
                  onClick={(e) => {
                    // 控制面板点击不触发上传；分割条拖拽后的合成点击或图片交换拖拽均不触发上传
                    const target = e.target as HTMLElement;
                    if (target?.closest('[data-control-panel="true"]') || suppressClickRef.current || controlActiveRef.current || dndSourceId) {
                      e.preventDefault();
                      e.stopPropagation();
                      return;
                    }
                    if (selectionEnabled) {
                      e.preventDefault();
                      e.stopPropagation();
                      onSlotSelectToggle && onSlotSelectToggle(slot.id);
                      return;
                    }
                    handleSlotClick(slot.id);
                  }}
                  onWheel={(e) => {
                    // Alt + 滚轮：缩放当前槽位图片（不再支持 Shift 加速）
                    if (selectionEnabled) return;
                    if (!slot.image) return;
                    if (!e.altKey) return;
                    e.preventDefault();
                    e.stopPropagation();
                    if (!onSlotsChange) return;
                    const baseStep = settings?.zoomStep ?? 0.1;
                    const mult = 1;
                    const dir = e.deltaY < 0 ? 1 : -1; // 上滚放大，下滚缩小
                    const delta = baseStep * mult * dir;
                    const current = slot.zoom || 1;
                    const minZSetting = settings?.zoomMin ?? 0.1;
                    const minZ = settings?.enforceMinZoomBySlotSize ? Math.max(1, minZSetting) : minZSetting;
                    const maxZ = settings?.zoomMax ?? 5;
                    const next = Math.min(maxZ, Math.max(minZ, current + delta));
                    if (next === current) return;
                    const updated: PuzzleSlot[] = slots.map((s) => {
                      if (s.id !== slot.id) return s;
                      const { offsetX, offsetY } = clampOffsetsFor(s, next);
                      return { ...s, zoom: next, offsetX, offsetY };
                    });
                    onSlotsChange(updated);
                  }}
                  onMouseUp={() => {}}
                  onMouseLeave={() => {}}
                  draggable={selectionEnabled ? false : (!!slot.image && !isPanning)}
                  onDragStart={(e) => {
                    // 平移过程中禁止触发换图拖拽
                    if (selectionEnabled || isPanning || panRef.current?.slotId === slot.id) {
                      e.preventDefault();
                      e.stopPropagation();
                      return;
                    }
                    if (!slot.image) return;
                    setDndSourceId(slot.id);
                    setDndTargetId(null);
                    try {
                      e.dataTransfer.setData('text/plain', slot.id);
                    } catch {}
                    e.dataTransfer.effectAllowed = 'move';
                  }}
                  onDragOver={(e) => {
                    if (selectionEnabled) return;
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    if (slot.id !== dndTargetId) setDndTargetId(slot.id);
                  }}
                  onDragLeave={() => {
                    if (selectionEnabled) return;
                    setDndTargetId((curr) => (curr === slot.id ? null : curr));
                  }}
                  onDrop={(e) => {
                    if (selectionEnabled) return;
                    e.preventDefault();
                    const srcId = (() => {
                      try {
                        return e.dataTransfer.getData('text/plain') || dndSourceId;
                      } catch {
                        return dndSourceId;
                      }
                    })();
                    if (srcId) swapSlotImages(srcId, slot.id);
                    setDndSourceId(null);
                    setDndTargetId(null);
                  }}
                  onDragEnd={() => {
                    if (selectionEnabled) return;
                    setDndSourceId(null);
                    setDndTargetId(null);
                  }}
                  title={selectionEnabled ? '点击选择/取消选择' : (slot.image ? '拖拽以交换图片；按住 Alt 并拖动以平移；Alt+滚轮缩放；点击更换图片' : '点击上传图片')}
                >
                  {!selectionEnabled && !slot.image && !dragging && !ratioDragging && (
                    <div className="w-full h-full pointer-events-none flex items-center justify-center bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded">
                      <div className="text-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <Upload className="w-6 h-6 mx-auto mb-1 text-gray-600" />
                      </div>
                    </div>
                  )}
                  {!selectionEnabled && slot.image && (
                    <div className={`w-full h-full ${(!dragging && !ratioDragging) ? 'group-hover:bg-black/10' : ''} bg-black/0 transition-colors duration-200 rounded relative`}>
                      {/* 顶部右侧控制面板（Tooltip 触发） */}
                      <div
                        className="absolute top-1 right-1 z-20 pointer-events-auto"
                        data-control-panel="true"
                        tabIndex={0}
                        onMouseEnter={() => openPanelNow(slot.id)}
                        onMouseLeave={() => scheduleClosePanel(slot.id)}
                        onPointerDown={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          // 手势开始：在抬起前全程抑制槽位点击
                          controlActiveRef.current = true;
                          suppressClickRef.current = true;
                          const clear = (ev: Event) => {
                            controlActiveRef.current = false;
                            suppressClickRef.current = false;
                            window.removeEventListener('pointerup', clear);
                            window.removeEventListener('mouseup', clear);
                          };
                          window.addEventListener('pointerup', clear);
                          window.addEventListener('mouseup', clear);
                        }}
                        onKeyDown={(e) => {
                          let dx = 0, dy = 0;
                          const key = e.key;
                          if (key === 'ArrowUp') dy = -1;
                          else if (key === 'ArrowDown') dy = 1;
                          else if (key === 'ArrowLeft') dx = -1;
                          else if (key === 'ArrowRight') dx = 1;
                          else return;
                          e.preventDefault();
                          e.stopPropagation();
                          const mult = 1;
                          moveOnce(slot.id, dx, dy, mult);
                        }}
                      >
                        <div className="relative group/panel">
                          <button
                            className="w-6 h-6 rounded-full bg-gray-900/70 text-white flex items-center justify-center hover:bg-gray-800 shadow-sm"
                            title="编辑"
                            aria-label="编辑"
                            onClick={(e) => { e.preventDefault(); e.stopPropagation(); }}
                          >
                            <MoreHorizontal className="w-4 h-4" />
                          </button>
                          {/* 外部无形桥接区：在按钮左侧创建一条更宽的透明带，覆盖上方缝隙，确保从左/左上方过渡到浮层不丢失 hover */}
                          {/* <span
                            aria-hidden="true"
                            className="absolute left-0 -translate-x-full top-1/2 -translate-y-1/2 w-6 h-6 rounded-full bg-transparent pointer-events-auto z-20"
                          /> */}
                          <div
                            className={`absolute right-0 top-0 p-1 rounded-md bg-gray-900/90 backdrop-blur-sm shadow-lg border border-white/10 opacity-0 pointer-events-none transition-opacity duration-150 w-max ${openPanelSlotId === slot.id ? 'opacity-100 pointer-events-auto' : ''} group-hover/panel:opacity-100 group-focus-within/panel:opacity-100 group-hover/panel:pointer-events-auto group-focus-within/panel:pointer-events-auto`}
                            onMouseEnter={() => openPanelNow(slot.id)}
                            onMouseLeave={() => scheduleClosePanel(slot.id)}
                          >
                            {/* 无形桥接区：向下  侧（靠近触发按钮）延伸，避免从左侧经过按钮时浮层消失 */}
                            {/* <span
                              aria-hidden="true"
                              className="absolute -right-0 top-9 -translate-y-1/2 w-6 h-6 rounded-full bg-transparent pointer-events-auto"
                            /> */}
                             <div className="grid grid-cols-4 gap-1">
                              {/* 第一排：放大 / 缩小 / 旋转 / 重置 */}
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="放大"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (!onSlotsChange) return;
                                  const step = settings?.zoomStep ?? 0.1;
                                  const maxZ = settings?.zoomMax ?? 5;
                                  const minZSetting = settings?.zoomMin ?? 0.1;
                                  const minZ = settings?.enforceMinZoomBySlotSize ? Math.max(1, minZSetting) : minZSetting;
                                  const updated: PuzzleSlot[] = slots.map((s) => {
                                    if (s.id !== slot.id) return s;
                                    const current = s.zoom || 1;
                                    const next = Math.min(maxZ, Math.max(minZ, current + step));
                                    if (next === current) return s;
                                    const { offsetX, offsetY } = clampOffsetsFor(s, next);
                                    return { ...s, zoom: next, offsetX, offsetY };
                                  });
                                  onSlotsChange(updated);
                                }}
                              >
                                <ZoomIn className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="缩小"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (!onSlotsChange) return;
                                  const step = settings?.zoomStep ?? 0.1;
                                  const maxZ = settings?.zoomMax ?? 5;
                                  const minZSetting = settings?.zoomMin ?? 0.1;
                                  const minZ = settings?.enforceMinZoomBySlotSize ? Math.max(1, minZSetting) : minZSetting;
                                  const updated: PuzzleSlot[] = slots.map((s) => {
                                    if (s.id !== slot.id) return s;
                                    const current = s.zoom || 1;
                                    const next = Math.min(maxZ, Math.max(minZ, current - step));
                                    if (next === current) return s;
                                    const { offsetX, offsetY } = clampOffsetsFor(s, next);
                                    return { ...s, zoom: next, offsetX, offsetY };
                                  });
                                  onSlotsChange(updated);
                                }}
                              >
                                <ZoomOut className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="旋转90°"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (!onSlotsChange) return;
                                  const updated: PuzzleSlot[] = slots.map((s) => {
                                    if (s.id !== slot.id) return s;
                                    const base = Math.round((s.rotation || 0) / 90) * 90;
                                    const reversed = !!(s.flipH !== s.flipV); // 奇数次镜像会反转旋转方向
                                    const delta = reversed ? -90 : 90;
                                    const next = (base + delta + 360) % 360;
                                    // 旋转后根据新姿态重夹偏移，避免露底
                                    const tmp = { ...s, rotation: next } as PuzzleSlot;
                                    const z = tmp.zoom || 1;
                                    const { offsetX, offsetY } = clampOffsetsFor(tmp, z);
                                    return { ...tmp, offsetX, offsetY };
                                  });
                                  onSlotsChange(updated);
                                }}
                              >
                                <RotateCw className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="重置"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (!onSlotsChange) return;
                                  const updated: PuzzleSlot[] = slots.map((s) => (s.id === slot.id ? { ...s, zoom: 1, rotation: 0, flipH: false, flipV: false, offsetX: 0, offsetY: 0 } : s));
                                  onSlotsChange(updated);
                                }}
                              >
                                <Undo2 className="w-4 h-4" />
                              </button>

                              {/* 第二排：方向移动（左 / 右 / 上 / 下）*/}
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="左移"
                                onPointerDown={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  const mult = 1;
                                  startHoldMove(slot.id, -1, 0, mult);
                                }}
                              >
                                <ArrowLeft className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="右移"
                                onPointerDown={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  const mult = 1;
                                  startHoldMove(slot.id, 1, 0, mult);
                                }}
                              >
                                <ArrowRight className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="上移"
                                onPointerDown={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  const mult = 1;
                                  startHoldMove(slot.id, 0, -1, mult);
                                }}
                              >
                                <ArrowUp className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="下移"
                                onPointerDown={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  const mult = 1;
                                  startHoldMove(slot.id, 0, 1, mult);
                                }}
                              >
                                <ArrowDown className="w-4 h-4" />
                              </button>
                              {/* 第三排：翻转 / 清除 / 居中 */}
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="水平翻转"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (!onSlotsChange) return;
                                  const updated: PuzzleSlot[] = slots.map((s) => {
                                    if (s.id !== slot.id) return s;
                                    const tmp = { ...s, flipH: !s.flipH } as PuzzleSlot;
                                    // 翻转后也重新夹偏移，确保边界安全
                                    const z = tmp.zoom || 1;
                                    const { offsetX, offsetY } = clampOffsetsFor(tmp, z);
                                    return { ...tmp, offsetX, offsetY };
                                  });
                                  onSlotsChange(updated);
                                }}
                              >
                                <FlipHorizontal className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="垂直翻转"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (!onSlotsChange) return;
                                  const updated: PuzzleSlot[] = slots.map((s) => {
                                    if (s.id !== slot.id) return s;
                                    const tmp = { ...s, flipV: !s.flipV } as PuzzleSlot;
                                    const z = tmp.zoom || 1;
                                    const { offsetX, offsetY } = clampOffsetsFor(tmp, z);
                                    return { ...tmp, offsetX, offsetY };
                                  });
                                  onSlotsChange(updated);
                                }}
                              >
                                <FlipVertical className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="清除图片"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (!onSlotsChange) return;
                                  const updated: PuzzleSlot[] = slots.map((s) => (s.id === slot.id ? { ...s, image: null, zoom: 1, rotation: 0, flipH: false, flipV: false, offsetX: 0, offsetY: 0 } : s));
                                  onSlotsChange(updated);
                                }}
                              >
                                <X className="w-4 h-4" />
                              </button>
                              <button
                                className="w-6 h-6 rounded-full bg-gray-800/80 text-white flex items-center justify-center hover:bg-gray-700"
                                title="居中（仅重置平移）"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (!onSlotsChange) return;
                                  const updated: PuzzleSlot[] = slots.map((s) => (s.id === slot.id ? { ...s, offsetX: 0, offsetY: 0 } : s));
                                  onSlotsChange(updated);
                                }}
                              >
                                <Crosshair className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* hover 提示改为更换图片（拖拽分割线或比例时隐藏） */}
                      {(!dragging && !ratioDragging) && (
                        <div className="absolute inset-0 z-10 pointer-events-none flex items-center justify-center">
                          <div className="text-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <Upload className="w-6 h-6 mx-auto mb-1 text-white" />
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            });
          })()}
        </div>

        {/* 可拖动分割线层 */}
        <div className="absolute inset-0 z-30 pointer-events-none">
          {/* 竖向分割线（仅在实际覆盖区显示）*/}
          {(() => {
            const size = computeCanvasSize();
            const pad = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
            const innerW = Math.max(1, size.width - (pad.left + pad.right));
            const innerH = Math.max(1, size.height - (pad.top + pad.bottom));
            const padLeftPct = (pad.left / size.width) * 100;
            const padTopPct = (pad.top / size.height) * 100;
            const innerWPct = (innerW / size.width) * 100;
            const innerHPct = (innerH / size.height) * 100;

            // 拖拽中：在独立模式下按当前 segments 逐段渲染，避免无覆盖区贯穿；联动模式下仍整条
            if (dragging?.type === 'v') {
              const pos = dragging.pos;
              if (settings?.independentDividerDrag) {
                const eps = 1e-6;
                return (
                  <>
                    {verticalSegments.filter(s => Math.abs(s.pos - pos) < eps).map((seg, idx) => (
                      <div
                        key={`drag-v-${idx}`}
                        className="absolute -translate-x-1/2 pointer-events-none opacity-100 transition-none"
                        style={{
                          left: `${padLeftPct + pos * innerWPct}%`,
                          top: `${padTopPct + seg.top * innerHPct}%`,
                          height: `${seg.height * innerHPct}%`,
                          willChange: 'left, top, height',
                          transform: 'translateZ(0)',
                        }}
                      >
                        <div className="pointer-events-none w-1 h-full bg-blue-500/40 transition-none" />
                        <div className="absolute inset-y-0 -left-2 right-2 pointer-events-none" />
                      </div>
                    ))}
                  </>
                );
              } else {
                const eps = 1e-6;
                return (
                  <>
                    {verticalSegments.filter(s => Math.abs(s.pos - pos) < eps).map((seg, idx) => (
                      <div
                        key={`drag-v-linked-${idx}`}
                        className="absolute -translate-x-1/2 pointer-events-none opacity-100 transition-none"
                        style={{
                          left: `${padLeftPct + pos * innerWPct}%`,
                          top: `${padTopPct + seg.top * innerHPct}%`,
                          height: `${seg.height * innerHPct}%`,
                          willChange: 'left, top, height',
                          transform: 'translateZ(0)',
                        }}
                      >
                        <div className="pointer-events-none w-1 h-full bg-blue-500/40 transition-none" />
                        <div className="absolute inset-y-0 -left-2 right-2 pointer-events-none" />
                      </div>
                    ))}
                  </>
                );
              }
            }

            return verticalSegments.map((seg, idx) => {
              let visible = false;
              if (hoveredDivider?.type === 'v' && Math.abs((hoveredDivider.pos ?? -1) - seg.pos) < 1e-6) {
                if ('top' in hoveredDivider && 'height' in hoveredDivider) {
                  // 仅在与悬停段重叠的部分显示
                  const hvTop = hoveredDivider.top;
                  const hvBottom = hoveredDivider.top + hoveredDivider.height;
                  const segTop = seg.top;
                  const segBottom = seg.top + seg.height;
                  const overlap = !(segBottom <= hvTop + 1e-6 || segTop >= hvBottom - 1e-6);
                  visible = overlap;
                } else {
                  visible = true;
                }
              }
              return (
              <div
                key={`v-${seg.pos}-${idx}`}
                className={`absolute -translate-x-1/2 pointer-events-none transition-none ${visible ? 'opacity-100' : 'opacity-0'}`}
                style={{
                  left: `${padLeftPct + seg.pos * innerWPct}%`,
                  top: `${padTopPct + seg.top * innerHPct}%`,
                  height: `${seg.height * innerHPct}%`,
                  willChange: 'left, top, height, opacity',
                  transform: 'translateZ(0)',
                }}
              >
                <div className="pointer-events-none w-1 h-full bg-blue-500/30 transition-none" />
                <div className="absolute inset-y-0 -left-2 right-2 pointer-events-none" />{/* 扩大命中区域（由容器接管事件）*/}
              </div>
            );
            });
          })()}

          {/* 横向分割线（仅在实际覆盖区显示）*/}
          {(() => {
            const size = computeCanvasSize();
            const pad = settings?.padding ?? { top: 0, right: 0, bottom: 0, left: 0 };
            const innerW = Math.max(1, size.width - (pad.left + pad.right));
            const innerH = Math.max(1, size.height - (pad.top + pad.bottom));
            const padLeftPct = (pad.left / size.width) * 100;
            const padTopPct = (pad.top / size.height) * 100;
            const innerWPct = (innerW / size.width) * 100;
            const innerHPct = (innerH / size.height) * 100;

            // 拖拽中：在独立模式下按当前 segments 逐段渲染；联动模式整条
            if (dragging?.type === 'h') {
              const pos = dragging.pos;
              if (settings?.independentDividerDrag) {
                const eps = 1e-6;
                return (
                  <>
                    {horizontalSegments.filter(s => Math.abs(s.pos - pos) < eps).map((seg, idx) => (
                      <div
                        key={`drag-h-${idx}`}
                        className="absolute -translate-y-1/2 pointer-events-none opacity-100 transition-none"
                        style={{
                          top: `${padTopPct + pos * innerHPct}%`,
                          left: `${padLeftPct + seg.left * innerWPct}%`,
                          width: `${seg.width * innerWPct}%`,
                          willChange: 'top, left, width',
                          transform: 'translateZ(0)',
                        }}
                      >
                        <div className="pointer-events-none h-1 w-full bg-blue-500/40 transition-none" />
                        <div className="absolute -top-2 -bottom-2 left-0 right-0 pointer-events-none" />
                      </div>
                    ))}
                  </>
                );
              } else {
                const eps = 1e-6;
                return (
                  <>
                    {horizontalSegments.filter(s => Math.abs(s.pos - pos) < eps).map((seg, idx) => (
                      <div
                        key={`drag-h-linked-${idx}`}
                        className="absolute -translate-y-1/2 pointer-events-none opacity-100 transition-none"
                        style={{
                          top: `${padTopPct + pos * innerHPct}%`,
                          left: `${padLeftPct + seg.left * innerWPct}%`,
                          width: `${seg.width * innerWPct}%`,
                          willChange: 'top, left, width',
                          transform: 'translateZ(0)',
                        }}
                      >
                        <div className="pointer-events-none h-1 w-full bg-blue-500/40 transition-none" />
                        <div className="absolute -top-2 -bottom-2 left-0 right-0 pointer-events-none" />
                      </div>
                    ))}
                  </>
                );
              }
            }

            return horizontalSegments.map((seg, idx) => {
              let visible = false;
              if (hoveredDivider?.type === 'h' && Math.abs((hoveredDivider.pos ?? -1) - seg.pos) < 1e-6) {
                if ('left' in hoveredDivider && 'width' in hoveredDivider) {
                  const hvLeft = hoveredDivider.left;
                  const hvRight = hoveredDivider.left + hoveredDivider.width;
                  const segLeft = seg.left;
                  const segRight = seg.left + seg.width;
                  const overlap = !(segRight <= hvLeft + 1e-6 || segLeft >= hvRight - 1e-6);
                  visible = overlap;
                } else {
                  visible = true;
                }
              }
              return (
              <div
                key={`h-${seg.pos}-${idx}`}
                className={`absolute -translate-y-1/2 pointer-events-none transition-none ${visible ? 'opacity-100' : 'opacity-0'}`}
                style={{
                  top: `${padTopPct + seg.pos * innerHPct}%`,
                  left: `${padLeftPct + seg.left * innerWPct}%`,
                  width: `${seg.width * innerWPct}%`,
                  willChange: 'top, left, width, opacity',
                  transform: 'translateZ(0)',
                }}
              >
                <div className="pointer-events-none h-1 w-full bg-blue-500/30 transition-none" />
                <div className="absolute -top-2 -bottom-2 left-0 right-0 pointer-events-none" />
              </div>
            );
            });
          })()}
        </div>
        {/* 画布边/角拖拽把手层（提高命中，固定显示 resize 指针） */}
        <div className="absolute inset-0 z-40 pointer-events-none select-none">
          {/* 左右边把手 */}
          <div
            className="absolute inset-y-0 left-0 w-3 pointer-events-auto"
            style={{ cursor: 'ew-resize' }}
            onMouseEnter={() => setHoveredEdge('left')}
            onMouseLeave={() => setHoveredEdge(null)}
            onMouseDown={(e) => { e.preventDefault(); e.stopPropagation(); beginRatioDrag('left', e.clientX, e.clientY); }}
          />
          <div
            className="absolute inset-y-0 right-0 w-3 pointer-events-auto"
            style={{ cursor: 'ew-resize' }}
            onMouseEnter={() => setHoveredEdge('right')}
            onMouseLeave={() => setHoveredEdge(null)}
            onMouseDown={(e) => { e.preventDefault(); e.stopPropagation(); beginRatioDrag('right', e.clientX, e.clientY); }}
          />
          {/* 上下边把手 */}
          <div
            className="absolute inset-x-0 top-0 h-3 pointer-events-auto"
            style={{ cursor: 'ns-resize' }}
            onMouseEnter={() => setHoveredEdge('top')}
            onMouseLeave={() => setHoveredEdge(null)}
            onMouseDown={(e) => { e.preventDefault(); e.stopPropagation(); beginRatioDrag('top', e.clientX, e.clientY); }}
          />
          <div
            className="absolute inset-x-0 bottom-0 h-3 pointer-events-auto"
            style={{ cursor: 'ns-resize' }}
            onMouseEnter={() => setHoveredEdge('bottom')}
            onMouseLeave={() => setHoveredEdge(null)}
            onMouseDown={(e) => { e.preventDefault(); e.stopPropagation(); beginRatioDrag('bottom', e.clientX, e.clientY); }}
          />
          {/* 四角把手 */}
          <div
            className="absolute -top-1 -left-1 w-4 h-4 pointer-events-auto"
            style={{ cursor: 'nwse-resize' }}
            onMouseEnter={() => setHoveredEdge('corner-nw')}
            onMouseLeave={() => setHoveredEdge(null)}
            onMouseDown={(e) => { e.preventDefault(); e.stopPropagation(); beginRatioDrag('corner-nw', e.clientX, e.clientY); }}
          />
          <div
            className="absolute -top-1 -right-1 w-4 h-4 pointer-events-auto"
            style={{ cursor: 'nesw-resize' }}
            onMouseEnter={() => setHoveredEdge('corner-ne')}
            onMouseLeave={() => setHoveredEdge(null)}
            onMouseDown={(e) => { e.preventDefault(); e.stopPropagation(); beginRatioDrag('corner-ne', e.clientX, e.clientY); }}
          />
          <div
            className="absolute -bottom-1 -right-1 w-4 h-4 pointer-events-auto"
            style={{ cursor: 'nwse-resize' }}
            onMouseEnter={() => setHoveredEdge('corner-se')}
            onMouseLeave={() => setHoveredEdge(null)}
            onMouseDown={(e) => { e.preventDefault(); e.stopPropagation(); beginRatioDrag('corner-se', e.clientX, e.clientY); }}
          />
          <div
            className="absolute -bottom-1 -left-1 w-4 h-4 pointer-events-auto"
            style={{ cursor: 'nesw-resize' }}
            onMouseEnter={() => setHoveredEdge('corner-sw')}
            onMouseLeave={() => setHoveredEdge(null)}
            onMouseDown={(e) => { e.preventDefault(); e.stopPropagation(); beginRatioDrag('corner-sw', e.clientX, e.clientY); }}
          />
        </div>
      </div>
    );
  }
);

PuzzleCanvas.displayName = 'PuzzleCanvas';

export default PuzzleCanvas;

