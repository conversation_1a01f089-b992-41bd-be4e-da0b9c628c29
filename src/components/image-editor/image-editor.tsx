'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import ImageUpload from './image-upload';
import ImageCanvas, { ImageCanvasRef, ImageFilters } from './image-canvas';
import { createDefaultCurves } from './curve-utils';
import ControlTabs from './control-tabs';
import Toolbar from './toolbar';
import { useHistory } from '@/hooks/use-history';

export default function ImageEditor() {
  const canvasRef = useRef<ImageCanvasRef>(null);
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const defaultFilters: ImageFilters = {
    // 基础调整
    brightness: 100,
    contrast: 100,
    saturation: 100,
    hue: 0,
    blur: 0,
    sepia: 0,
    grayscale: 0,

    // 高级色彩调整
    exposure: 0,
    highlights: 0,
    shadows: 0,
    whites: 0,
    blacks: 0,
    temperature: 0,
    tint: 0,
    vibrance: 0,

    // 色彩曲线
    curves: createDefaultCurves(),

    // 视图控制
    zoom: 100,

    // 几何变换
    rotation: 0,
    flipHorizontal: false,
    flipVertical: false,

    // 高级滤镜
    sharpen: 0,
    noise: 0,
    vignette: 0,
    clarity: 0
  };

  // 使用历史管理
  const {
    currentState: filters,
    canUndo,
    canRedo,
    pushState,
    startDragging,
    endDragging,
    undo,
    redo,
    reset: resetHistory,
    cleanup
  } = useHistory(defaultFilters, {
    maxHistorySize: 50,
    debounceMs: 300
  });

  // 文件验证函数
  const validateFile = useCallback((file: File): { isValid: boolean; error?: string } => {
    const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (!validTypes.includes(file.type)) {
      return { isValid: false, error: '不支持的文件格式。支持 JPG、PNG、WebP 和 GIF 格式。' };
    }

    if (file.size > maxSize) {
      return { isValid: false, error: '文件大小超过 50MB 限制。' };
    }

    return { isValid: true };
  }, []);

  const handleImageUpload = useCallback((file: File) => {
    // 验证文件
    const validation = validateFile(file);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      setImage(img);
      // 重置滤镜和历史记录
      resetHistory();
      URL.revokeObjectURL(url);
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      console.error('Failed to load image');
      alert('图片加载失败，请检查文件是否损坏。');
    };

    img.src = url;
  }, [resetHistory, validateFile]);

  const handleFiltersChange = useCallback((newFilters: ImageFilters, action?: string, immediate = false) => {
    pushState(newFilters, action, immediate);
  }, [pushState]);

  const handleZoomChange = useCallback((newZoom: number, immediate = false) => {
    handleFiltersChange({ ...filters, zoom: newZoom }, 'zoom_change', immediate);
  }, [filters, handleFiltersChange]);

  const resetAllFilters = useCallback(() => {
    // 重置历史记录到初始状态（包含缩放重置到100%）
    resetHistory();
  }, [resetHistory]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'z':
            if (e.shiftKey) {
              e.preventDefault();
              redo();
            } else {
              e.preventDefault();
              undo();
            }
            break;
          case 'y':
            e.preventDefault();
            redo();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [undo, redo]);

  // 清理定时器
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  if (!image) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50 dark:from-slate-900 dark:via-slate-900 dark:to-indigo-950/50 relative overflow-hidden">
        {/* 增强的背景装饰 */}
        <div className="absolute inset-0 overflow-hidden">
          {/* 主要装饰球 */}
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/25 to-purple-600/25 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/25 to-pink-600/25 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>

          {/* 新增的小装饰元素 */}
          <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-emerald-400/20 to-teal-600/20 rounded-full blur-2xl animate-bounce" style={{ animationDuration: '3s' }}></div>
          <div className="absolute bottom-20 right-20 w-24 h-24 bg-gradient-to-br from-rose-400/20 to-orange-600/20 rounded-full blur-2xl animate-bounce" style={{ animationDuration: '4s', animationDelay: '1s' }}></div>

          {/* 网格背景 */}
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.15)_1px,transparent_0)] dark:bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.05)_1px,transparent_0)] bg-[size:24px_24px] opacity-30"></div>
        </div>

        <div className="relative flex items-center justify-center min-h-screen p-8">
          <div className="max-w-2xl w-full">
            {/* 增强的标题区域 */}
            <div className="text-center mb-12">
              <div className="mb-8">
                <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-slate-900 via-blue-800 to-indigo-900 dark:from-white dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent mb-4 tracking-tight">
                  LensKiss
                </h1>
                <p className="text-xl text-slate-600 dark:text-slate-300 font-light leading-relaxed">
                  专业级图片编辑器，让每一张照片都闪闪发光
                </p>
              </div>

              {/* 增强的特性标签 */}
              <div className="flex flex-wrap justify-center gap-3">
                <span className="group px-5 py-3 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl text-sm font-semibold text-slate-700 dark:text-slate-300 border border-slate-200/60 dark:border-slate-700/60 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-950/50 dark:hover:to-indigo-950/50">
                  <span className="mr-2 text-lg group-hover:animate-bounce">🎨</span>
                  专业调色
                </span>
                <span className="group px-5 py-3 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl text-sm font-semibold text-slate-700 dark:text-slate-300 border border-slate-200/60 dark:border-slate-700/60 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-teal-50 dark:hover:from-emerald-950/50 dark:hover:to-teal-950/50">
                  <span className="mr-2 text-lg group-hover:animate-pulse">⚡</span>
                  实时预览
                </span>
                <span className="group px-5 py-3 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md rounded-2xl text-sm font-semibold text-slate-700 dark:text-slate-300 border border-slate-200/60 dark:border-slate-700/60 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 dark:hover:from-purple-950/50 dark:hover:to-pink-950/50">
                  <span className="mr-2 text-lg group-hover:animate-spin">📱</span>
                  极速响应
                </span>
              </div>
            </div>

            <ImageUpload onImageUpload={handleImageUpload} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50 dark:from-slate-900 dark:via-slate-900 dark:to-indigo-950/50 flex flex-col relative overflow-hidden">
      {/* 增强的背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* 主要装饰球 */}
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-blue-400/15 to-purple-600/15 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '4s' }}></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-tr from-indigo-400/15 to-pink-600/15 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '5s', animationDelay: '1s' }}></div>

        {/* 新增的动态装饰 */}
        <div className="absolute top-1/3 left-10 w-32 h-32 bg-gradient-to-br from-emerald-400/12 to-teal-600/12 rounded-full blur-2xl animate-bounce" style={{ animationDuration: '6s' }}></div>
        <div className="absolute bottom-1/3 right-10 w-40 h-40 bg-gradient-to-br from-rose-400/12 to-orange-600/12 rounded-full blur-2xl animate-bounce" style={{ animationDuration: '7s', animationDelay: '2s' }}></div>

        {/* 微妙的网格背景 */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.1)_1px,transparent_0)] dark:bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.03)_1px,transparent_0)] bg-[size:32px_32px] opacity-40"></div>
      </div>

      {/* 工具栏 */}
      <Toolbar
        canvasRef={canvasRef}
        onReset={resetAllFilters}
        disabled={!image}
        canUndo={canUndo}
        canRedo={canRedo}
        onUndo={undo}
        onRedo={redo}
        image={image}
        filters={filters}
        zoom={filters.zoom}
        onImageUpload={handleImageUpload}
      />

      {/* 主要内容区域 */}
      <div className="flex-1 flex min-h-0 relative">
        {/* 画布区域 */}
        <div className="flex-1 flex flex-col min-w-0">
          <div className="flex-1 overflow-auto p-8" style={{ maxHeight: 'calc(100vh - 200px)' }}>
            <div className="min-h-full flex items-center">
              <div className="relative group w-fit mx-auto">
                <ImageCanvas
                  ref={canvasRef}
                  image={image}
                  filters={filters}
                  onZoomChange={handleZoomChange}
                  className="max-w-none shadow-2xl transition-all duration-500 group-hover:shadow-3xl rounded-2xl overflow-hidden"
                />

                {/* 多层装饰效果 */}
                <div className="absolute -inset-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 rounded-3xl -z-10 blur-3xl opacity-50 group-hover:opacity-70 transition-all duration-500 animate-pulse" style={{ animationDuration: '3s' }}></div>
                <div className="absolute -inset-4 bg-gradient-to-r from-cyan-400/15 via-blue-500/15 to-violet-500/15 rounded-2xl -z-10 blur-xl opacity-60 group-hover:opacity-80 transition-all duration-300"></div>
                <div className="absolute -inset-2 bg-gradient-to-r from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-900/40 rounded-xl -z-10 backdrop-blur-sm border border-white/20 dark:border-slate-700/20"></div>

                {/* 增强的画布角落装饰 */}
                <div className="absolute -top-3 -left-3 w-6 h-6 border-t-3 border-l-3 border-blue-400/70 rounded-tl-xl transition-all duration-300 group-hover:border-blue-500 group-hover:scale-110"></div>
                <div className="absolute -top-3 -right-3 w-6 h-6 border-t-3 border-r-3 border-purple-400/70 rounded-tr-xl transition-all duration-300 group-hover:border-purple-500 group-hover:scale-110"></div>
                <div className="absolute -bottom-3 -left-3 w-6 h-6 border-b-3 border-l-3 border-indigo-400/70 rounded-bl-xl transition-all duration-300 group-hover:border-indigo-500 group-hover:scale-110"></div>
                <div className="absolute -bottom-3 -right-3 w-6 h-6 border-b-3 border-r-3 border-violet-400/70 rounded-br-xl transition-all duration-300 group-hover:border-violet-500 group-hover:scale-110"></div>

                {/* 新增的光效装饰 */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-blue-400/60 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-16 bg-gradient-to-b from-transparent via-indigo-400/60 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-1 h-16 bg-gradient-to-b from-transparent via-violet-400/60 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          </div>


        </div>

        {/* 控制面板侧边栏 - PC端现代化设计 */}
        <div className="flex-shrink-0 w-96 border-l border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-b from-white/95 via-white/90 to-slate-50/95 dark:from-slate-900/95 dark:via-slate-900/90 dark:to-slate-800/95 backdrop-blur-sm relative">
          {/* 侧边栏装饰 */}
          <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-500 via-purple-500 to-indigo-500 opacity-60"></div>
          <ControlTabs
            filters={filters}
            onFiltersChange={handleFiltersChange}
            canvasRef={canvasRef}
            image={image}
            disabled={!image}
            className="h-full"
            onDragStart={startDragging}
            onDragEnd={endDragging}
            zoom={filters.zoom}
            onZoomChange={handleZoomChange}
          />
        </div>
      </div>
    </div>
  );
}
