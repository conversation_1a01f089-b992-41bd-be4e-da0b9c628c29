'use client';

import { useState } from 'react';
import {
  RotateCcw,
  Download,
  Share2,
  Info,
  Maximize2,
  Minimize2,
  Undo,
  Redo,
  Upload
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ImageCanvasRef, ImageFilters } from './image-canvas';
import ImageSizeInfo from './image-size-info';

interface ToolbarProps {
  canvasRef: React.RefObject<ImageCanvasRef | null>;
  onReset: () => void;
  disabled?: boolean;
  className?: string;
  canUndo?: boolean;
  canRedo?: boolean;
  onUndo?: () => void;
  onRedo?: () => void;
  image?: HTMLImageElement | null;
  filters?: ImageFilters;
  zoom?: number;
  onImageUpload?: (file: File) => void;
}

export default function Toolbar({
  canvasRef,
  onReset,
  disabled = false,
  className = '',
  canUndo = false,
  canRedo = false,
  onUndo,
  onRedo,
  image,
  filters,
  zoom,
  onImageUpload
}: ToolbarProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 导出设置状态
  const [exportFormat, setExportFormat] = useState<'png' | 'jpeg'>('png');
  const [exportQuality, setExportQuality] = useState(90);
  const [useDisplaySize, setUseDisplaySize] = useState(true);
  const [enablePngCompression, setEnablePngCompression] = useState(false);
  const [pngCompressionQuality, setPngCompressionQuality] = useState(80);

  const handleDownload = async () => {
    if (!canvasRef.current) return;

    const actualZoom = zoom || 100;
    const qualityValue = exportFormat === 'jpeg' ? exportQuality / 100 : 1;

    await canvasRef.current.downloadImage(
      exportFormat,
      qualityValue,
      false,
      useDisplaySize,
      actualZoom,
      // PNG压缩选项
      exportFormat === 'png' && enablePngCompression ? {
        enableCompression: true,
        compressionQuality: pngCompressionQuality / 100
      } : undefined
    );
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && onImageUpload) {
      onImageUpload(file);
    }
  };

  const handleShare = async () => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current.getCanvas();
    if (!canvas) return;

    try {
      canvas.toBlob(async (blob) => {
        if (!blob) return;

        if (navigator.share && navigator.canShare) {
          const file = new File([blob], 'edited-image.png', { type: 'image/png' });
          if (navigator.canShare({ files: [file] })) {
            await navigator.share({
              title: '编辑后的图片',
              files: [file]
            });
            return;
          }
        }

        // 降级到复制到剪贴板
        if (navigator.clipboard && navigator.clipboard.write) {
          const item = new ClipboardItem({ 'image/png': blob });
          await navigator.clipboard.write([item]);
          alert('图片已复制到剪贴板');
        } else {
          // 最后降级到下载
          handleDownload();
        }
      }, 'image/png');
    } catch (error) {
      console.error('分享失败:', error);
      handleDownload();
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const showImageInfo = () => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current.getCanvas();
    if (!canvas) return;

    const imageData = canvasRef.current.getImageData();
    if (!imageData) return;

    const info = `
图片信息:
尺寸: ${imageData.width} × ${imageData.height}
像素数: ${(imageData.width * imageData.height).toLocaleString()}
色彩空间: sRGB
位深度: 8位/通道
    `.trim();

    alert(info);
  };

  if (!image) return null;

  return (
    <div className={`bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200/50 dark:border-gray-700/50 px-6 py-3 ${className}`}>
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        {/* 左侧工具组 */}
        <div className="flex items-center gap-4">
          {/* 历史操作组 */}
          <div className="flex items-center bg-gray-50 dark:bg-gray-800 rounded-lg p-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onUndo}
              disabled={disabled || !canUndo}
              className="h-7 w-7 p-0 hover:bg-white dark:hover:bg-gray-700 rounded-md transition-all duration-200"
              title="撤销 (Ctrl+Z)"
            >
              <Undo className="w-3.5 h-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onRedo}
              disabled={disabled || !canRedo}
              className="h-7 w-7 p-0 hover:bg-white dark:hover:bg-gray-700 rounded-md transition-all duration-200"
              title="重做 (Ctrl+Y)"
            >
              <Redo className="w-3.5 h-3.5" />
            </Button>
          </div>

          {/* 重置按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={onReset}
            disabled={disabled}
            className="h-8 px-3 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
            title="重置所有调整"
          >
            <RotateCcw className="w-3.5 h-3.5 mr-1.5" />
            <span className="text-xs">重置</span>
          </Button>

          {/* 更换图片按钮 */}
          {onImageUpload && (
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                title="更换图片"
              />
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-3 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
                title="更换图片"
              >
                <Upload className="w-3.5 h-3.5 mr-1.5" />
                <span className="text-xs">更换图片</span>
              </Button>
            </div>
          )}
        </div>

        {/* 中间区域 - 图像尺寸信息或标题 */}
        <div className="flex-1 flex items-center justify-center">
          {image && filters ? (
            <ImageSizeInfo
              image={image}
              filters={filters}
              zoom={zoom || filters?.zoom || 100}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-200/50 dark:border-gray-700/50"
            />
          ) : (
            <h1 className="text-lg font-semibold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
              图片编辑器
            </h1>
          )}
        </div>

        {/* 右侧工具组 */}
        <div className="flex items-center gap-2">
          {/* 工具按钮组 */}
          <div className="flex items-center bg-gray-50 dark:bg-gray-800 rounded-lg p-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={showImageInfo}
              disabled={disabled}
              className="h-7 w-7 p-0 hover:bg-white dark:hover:bg-gray-700 rounded-md transition-all duration-200"
              title="图片信息"
            >
              <Info className="w-3.5 h-3.5" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFullscreen}
              className="h-7 w-7 p-0 hover:bg-white dark:hover:bg-gray-700 rounded-md transition-all duration-200"
              title={isFullscreen ? "退出全屏" : "全屏"}
            >
              {isFullscreen ? (
                <Minimize2 className="w-3.5 h-3.5" />
              ) : (
                <Maximize2 className="w-3.5 h-3.5" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              disabled={disabled}
              className="h-7 w-7 p-0 hover:bg-white dark:hover:bg-gray-700 rounded-md transition-all duration-200"
              title="分享图片"
            >
              <Share2 className="w-3.5 h-3.5" />
            </Button>
          </div>

          {/* 主要操作按钮 - 下载带设置 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                disabled={disabled}
                className="h-8 px-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white border-0 shadow-sm transition-all duration-200"
              >
                <Download className="w-3.5 h-3.5 mr-1.5" />
                <span className="text-xs font-medium">下载</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 p-4">
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Download className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                    导出设置
                  </h3>
                </div>

                {/* 格式选择 */}
                <div className="space-y-2">
                  <Label className="text-xs font-medium">格式</Label>
                  <Select value={exportFormat} onValueChange={(value: 'png' | 'jpeg') => setExportFormat(value)}>
                    <SelectTrigger className="w-full h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="jpeg">JPEG</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* JPEG质量设置 */}
                {exportFormat === 'jpeg' && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label className="text-xs font-medium">质量</Label>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {exportQuality}%
                      </span>
                    </div>
                    <input
                      type="range"
                      min={10}
                      max={100}
                      step={5}
                      value={exportQuality}
                      onChange={(e) => setExportQuality(Number(e.target.value))}
                      className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                )}

                {/* PNG压缩选项 */}
                {exportFormat === 'png' && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs font-medium">启用PNG压缩</Label>
                      <Switch
                        checked={enablePngCompression}
                        onCheckedChange={setEnablePngCompression}
                      />
                    </div>

                    {enablePngCompression && (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <Label className="text-xs font-medium">压缩质量</Label>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {pngCompressionQuality}%
                          </span>
                        </div>
                        <input
                          type="range"
                          min={10}
                          max={100}
                          step={5}
                          value={pngCompressionQuality}
                          onChange={(e) => setPngCompressionQuality(Number(e.target.value))}
                          className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* 显示尺寸导出开关 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs font-medium">按显示尺寸导出</Label>
                    <Switch
                      checked={useDisplaySize}
                      onCheckedChange={setUseDisplaySize}
                    />
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {useDisplaySize
                      ? `将按当前显示尺寸导出（${Math.round(zoom || 100)}%缩放）`
                      : '将按图片实际尺寸导出（忽略视图缩放）'
                    }
                  </p>
                </div>

                {/* 下载按钮 */}
                <Button
                  onClick={handleDownload}
                  disabled={disabled}
                  className="w-full flex items-center gap-2 mt-4"
                >
                  <Download className="w-4 h-4" />
                  下载图片
                </Button>

                {/* 格式说明 */}
                <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  {exportFormat === 'png'
                    ? enablePngCompression
                      ? 'PNG 格式支持透明度，已启用压缩以减小文件大小'
                      : 'PNG 格式支持透明度，文件较大'
                    : 'JPEG 格式文件较小，但不支持透明度'
                  }
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
