'use client';

import { useMemo } from 'react';
import { ImageFilters } from './image-canvas';

interface ImageSizeInfoProps {
  image: HTMLImageElement | null;
  filters: ImageFilters;
  zoom?: number;
  className?: string;
}

// 计算旋转后的边界框尺寸
const calculateRotatedBounds = (width: number, height: number, rotation: number) => {
  // 将角度转换为弧度
  const radians = (rotation * Math.PI) / 180;

  // 计算旋转后的边界框
  const cos = Math.abs(Math.cos(radians));
  const sin = Math.abs(Math.sin(radians));

  const rotatedWidth = width * cos + height * sin;
  const rotatedHeight = width * sin + height * cos;

  return {
    width: Math.ceil(rotatedWidth),
    height: Math.ceil(rotatedHeight)
  };
};

export default function ImageSizeInfo({ image, filters, zoom = 100, className = '' }: ImageSizeInfoProps) {
  const sizeInfo = useMemo(() => {
    if (!image) return null;

    const originalWidth = image.width;
    const originalHeight = image.height;

    // 计算当前变换后的尺寸
    const currentBounds = calculateRotatedBounds(
      originalWidth,
      originalHeight,
      filters.rotation
    );

    // 计算视图显示尺寸（应用zoom）
    const zoomFactor = zoom / 100;
    const displayBounds = {
      width: Math.round(currentBounds.width * zoomFactor),
      height: Math.round(currentBounds.height * zoomFactor)
    };

    // 检查是否有变换
    const hasTransform =
      filters.rotation !== 0 ||
      filters.flipHorizontal ||
      filters.flipVertical;

    // 检查是否有视图缩放
    const hasZoom = zoom !== 100;

    return {
      original: { width: originalWidth, height: originalHeight },
      current: { width: currentBounds.width, height: currentBounds.height },
      display: { width: displayBounds.width, height: displayBounds.height },
      hasTransform,
      hasZoom
    };
  }, [image, filters.rotation, filters.flipHorizontal, filters.flipVertical, zoom]);

  if (!sizeInfo) return null;

  const formatSize = (width: number, height: number) => `${width} × ${height}`;

  return (
    <div className={`flex items-center gap-3 text-xs ${className}`}>
      {/* 原始尺寸 */}
      <div className="flex items-center gap-1.5">
        <span className="text-gray-500 dark:text-gray-400">原始:</span>
        <span className="font-mono text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
          {formatSize(sizeInfo.original.width, sizeInfo.original.height)}
        </span>
      </div>

      {/* 当前尺寸 - 只在有变换时显示 */}
      {sizeInfo.hasTransform && (
        <>
          <div className="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
          <div className="flex items-center gap-1.5">
            <span className="text-gray-500 dark:text-gray-400">当前:</span>
            <span className="font-mono text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded">
              {formatSize(sizeInfo.current.width, sizeInfo.current.height)}
            </span>
          </div>
        </>
      )}

      {/* 显示尺寸 - 只在有视图缩放时显示 */}
      {sizeInfo.hasZoom && (
        <>
          <div className="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
          <div className="flex items-center gap-1.5">
            <span className="text-gray-500 dark:text-gray-400">显示:</span>
            <span className="font-mono text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/30 px-2 py-1 rounded">
              {formatSize(sizeInfo.display.width, sizeInfo.display.height)}
            </span>
            <span className="text-xs text-gray-400 dark:text-gray-500">
              缩放比例: {zoom}%
            </span>
          </div>
        </>
      )}
    </div>
  );
}
