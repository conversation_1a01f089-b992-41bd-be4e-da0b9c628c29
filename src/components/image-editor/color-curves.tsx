'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { RotateCcw, Palette } from 'lucide-react';
// 仅类型导入，避免与本地值冲突
import type { ColorCurves, CurvePoint } from './image-canvas';
import { 
  addCurvePoint, 
  removeCurvePoint, 
  resetCurve, 
  interpolateCurve,
  CURVE_PRESETS 
} from './curve-utils';

interface ColorCurvesProps {
  curves: ColorCurves;
  onCurvesChange: (curves: ColorCurves) => void;
  onDragStart?: () => void;
  onDragEnd?: () => void;
  className?: string;
}

type CurveChannel = 'rgb' | 'red' | 'green' | 'blue';

const CHANNEL_COLORS = {
  rgb: '#666666',
  red: '#ef4444',
  green: '#22c55e',
  blue: '#3b82f6'
};

const CHANNEL_LABELS = {
  rgb: 'RGB',
  red: '红',
  green: '绿',
  blue: '蓝'
};

export default function ColorCurves({
  curves,
  onCurvesChange,
  onDragStart,
  onDragEnd,
  className = ''
}: ColorCurvesProps) {
  const [activeChannel, setActiveChannel] = useState<CurveChannel>('rgb');
  const [dragIndex, setDragIndex] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const svgRef = useRef<SVGSVGElement>(null);
  
  const CURVE_SIZE = 256;
  const GRID_SIZE = 32;

  // 坐标转换函数
  const valueToSVG = useCallback((value: number) => {
    return CURVE_SIZE - (value / 255) * CURVE_SIZE;
  }, []);

  const svgToValue = useCallback((svgY: number) => {
    return Math.round(255 - (svgY / CURVE_SIZE) * 255);
  }, []);

  // 生成曲线路径
  const generateCurvePath = useCallback((points: CurvePoint[]) => {
    if (points.length < 2) return '';
    
    const sortedPoints = [...points].sort((a, b) => a.x - b.x);
    let path = `M ${sortedPoints[0].x} ${valueToSVG(sortedPoints[0].y)}`;
    
    // 为每个像素生成路径点以获得平滑曲线
    for (let x = sortedPoints[0].x + 1; x <= sortedPoints[sortedPoints.length - 1].x; x++) {
      const y = interpolateCurve(sortedPoints, x);
      path += ` L ${x} ${valueToSVG(y)}`;
    }
    
    return path;
  }, [valueToSVG]);

  // 处理鼠标事件
  const handleMouseDown = useCallback((e: React.MouseEvent, pointIndex?: number) => {
    e.preventDefault();
    setIsDragging(true);
    onDragStart?.();
    
    if (pointIndex !== undefined) {
      setDragIndex(pointIndex);
    } else {
      // 在曲线上添加新点
      const rect = svgRef.current?.getBoundingClientRect();
      if (!rect) return;
      
      const x = Math.round(e.clientX - rect.left);
      const y = svgToValue(e.clientY - rect.top);
      
      if (x >= 0 && x <= 255 && y >= 0 && y <= 255) {
        const newPoints = addCurvePoint(curves[activeChannel], x, y);
        const newCurves = { ...curves, [activeChannel]: newPoints };
        onCurvesChange(newCurves);
        
        // 找到新添加点的索引
        const newIndex = newPoints.findIndex(p => p.x === x);
        setDragIndex(newIndex);
      }
    }
  }, [activeChannel, curves, onCurvesChange, onDragStart, svgToValue]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || dragIndex === null) return;
    
    const rect = svgRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const x = Math.round(e.clientX - rect.left);
    const y = svgToValue(e.clientY - rect.top);
    
    // 限制坐标范围
    const clampedX = Math.max(0, Math.min(255, x));
    const clampedY = Math.max(0, Math.min(255, y));
    
    const currentPoints = curves[activeChannel];
    const newPoints = [...currentPoints];
    
    // 限制端点只能垂直移动
    if (dragIndex === 0) {
      newPoints[dragIndex] = { x: 0, y: clampedY };
    } else if (dragIndex === currentPoints.length - 1) {
      newPoints[dragIndex] = { x: 255, y: clampedY };
    } else {
      newPoints[dragIndex] = { x: clampedX, y: clampedY };
    }
    
    // 按x坐标排序
    newPoints.sort((a, b) => a.x - b.x);
    
    const newCurves = { ...curves, [activeChannel]: newPoints };
    onCurvesChange(newCurves);
  }, [isDragging, dragIndex, activeChannel, curves, onCurvesChange, svgToValue]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragIndex(null);
    onDragEnd?.();
  }, [onDragEnd]);

  // 双击删除点
  const handleDoubleClick = useCallback((e: React.MouseEvent, pointIndex: number) => {
    e.preventDefault();
    if (pointIndex > 0 && pointIndex < curves[activeChannel].length - 1) {
      const newPoints = removeCurvePoint(curves[activeChannel], pointIndex);
      const newCurves = { ...curves, [activeChannel]: newPoints };
      onCurvesChange(newCurves);
    }
  }, [activeChannel, curves, onCurvesChange]);

  // 重置当前通道
  const resetCurrentChannel = useCallback(() => {
    const newCurves = { ...curves, [activeChannel]: resetCurve() };
    onCurvesChange(newCurves);
  }, [activeChannel, curves, onCurvesChange]);

  // 应用预设
  const applyPreset = useCallback((presetName: keyof typeof CURVE_PRESETS) => {
    const preset = CURVE_PRESETS[presetName];
    const newCurves = { ...curves, ...preset };
    onCurvesChange(newCurves);
  }, [curves, onCurvesChange]);

  // 绑定全局鼠标事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 通道选择器 */}
      <div className="flex items-center gap-2">
        {(Object.keys(CHANNEL_COLORS) as CurveChannel[]).map((channel) => (
          <Button
            key={channel}
            variant={activeChannel === channel ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveChannel(channel)}
            className="h-8 px-3 text-xs"
            style={{
              backgroundColor: activeChannel === channel ? CHANNEL_COLORS[channel] : undefined,
              borderColor: CHANNEL_COLORS[channel],
              color: activeChannel === channel ? 'white' : CHANNEL_COLORS[channel]
            }}
          >
            {CHANNEL_LABELS[channel]}
          </Button>
        ))}
        
        <div className="flex-1" />
        
        <Button
          variant="outline"
          size="sm"
          onClick={resetCurrentChannel}
          className="h-8 px-3 text-xs"
          title="重置当前通道"
        >
          <RotateCcw className="w-3 h-3" />
        </Button>
      </div>

      {/* 曲线编辑器 */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div className="relative">
          {/* Y轴刻度标签 */}
          <div className="absolute left-0 top-0 h-64 flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400 pointer-events-none">
            <span className="transform -translate-y-1/2">255</span>
            <span className="transform -translate-y-1/2">192</span>
            <span className="transform -translate-y-1/2">128</span>
            <span className="transform -translate-y-1/2">64</span>
            <span className="transform -translate-y-1/2">0</span>
          </div>

          <svg
            ref={svgRef}
            width={CURVE_SIZE}
            height={CURVE_SIZE}
            viewBox={`0 0 ${CURVE_SIZE} ${CURVE_SIZE}`}
            className="border border-gray-300 dark:border-gray-600 cursor-crosshair ml-8"
            onMouseDown={handleMouseDown}
          >
            {/* 网格 */}
            <defs>
              <pattern id="grid" width={GRID_SIZE} height={GRID_SIZE} patternUnits="userSpaceOnUse">
                <path
                  d={`M ${GRID_SIZE} 0 L 0 0 0 ${GRID_SIZE}`}
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="0.5"
                  className="text-gray-300 dark:text-gray-600"
                />
              </pattern>
            </defs>
            <rect width={CURVE_SIZE} height={CURVE_SIZE} fill="url(#grid)" />

            {/* 主要刻度线 - 垂直线 */}
            {[0, 64, 128, 192, 255].map((x) => (
              <line
                key={`v-${x}`}
                x1={x}
                y1="0"
                x2={x}
                y2={CURVE_SIZE}
                stroke="currentColor"
                strokeWidth="1"
                className="text-gray-400 dark:text-gray-500"
                opacity="0.6"
              />
            ))}

            {/* 主要刻度线 - 水平线 */}
            {[0, 64, 128, 192, 255].map((y) => (
              <line
                key={`h-${y}`}
                x1="0"
                y1={valueToSVG(y)}
                x2={CURVE_SIZE}
                y2={valueToSVG(y)}
                stroke="currentColor"
                strokeWidth="1"
                className="text-gray-400 dark:text-gray-500"
                opacity="0.6"
              />
            ))}

            {/* 对角线参考线 */}
            <line
              x1="0"
              y1={CURVE_SIZE}
              x2={CURVE_SIZE}
              y2="0"
              stroke="currentColor"
              strokeWidth="1"
              strokeDasharray="4,4"
              className="text-gray-400 dark:text-gray-500"
            />

            {/* 曲线 */}
            <path
              d={generateCurvePath(curves[activeChannel])}
              fill="none"
              stroke={CHANNEL_COLORS[activeChannel]}
              strokeWidth="2"
            />

            {/* 控制点 */}
            {curves[activeChannel].map((point, index) => (
              <circle
                key={index}
                cx={point.x}
                cy={valueToSVG(point.y)}
                r="4"
                fill={CHANNEL_COLORS[activeChannel]}
                stroke="white"
                strokeWidth="2"
                className="cursor-pointer hover:r-5 transition-all"
                onMouseDown={(e) => handleMouseDown(e, index)}
                onDoubleClick={(e) => handleDoubleClick(e, index)}
              />
            ))}
          </svg>

          {/* X轴刻度标签 */}
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1 ml-8 w-64">
            <span>0</span>
            <span>64</span>
            <span>128</span>
            <span>192</span>
            <span>255</span>
          </div>
        </div>
        
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          点击添加控制点，拖拽调整，双击删除（端点除外）。刻度线显示输入/输出值范围 0-255
        </div>
      </div>

      {/* 预设按钮 */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">曲线预设</h4>
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => applyPreset('contrast')}
            className="h-8 text-xs"
          >
            增强对比
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => applyPreset('soft')}
            className="h-8 text-xs"
          >
            柔和曲线
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => applyPreset('highlightCompress')}
            className="h-8 text-xs"
          >
            压制高光
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => applyPreset('shadowLift')}
            className="h-8 text-xs"
          >
            提升阴影
          </Button>
        </div>
      </div>
    </div>
  );
}
