'use client';

import { useState } from 'react';
import { Download, FileImage } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ImageCanvasRef } from './image-canvas';

interface ExportControlsProps {
  canvasRef: React.RefObject<ImageCanvasRef | null>;
  disabled?: boolean;
  className?: string;
  zoom?: number;
}

export default function ExportControls({ canvasRef, disabled = false, className = '', zoom }: ExportControlsProps) {
  const actualZoom = zoom || 100;
  const [format, setFormat] = useState<'png' | 'jpeg'>('png');
  const [quality, setQuality] = useState(90);
  const [useDisplaySize, setUseDisplaySize] = useState(true); // 默认开启显示尺寸导出
  const [enablePngCompression, setEnablePngCompression] = useState(false);
  const [pngCompressionQuality, setPngCompressionQuality] = useState(80);

  const handleExport = async () => {
    if (!canvasRef.current) return;

    const qualityValue = format === 'jpeg' ? quality / 100 : 1;
    // 根据用户选择决定是否使用显示尺寸导出
    await canvasRef.current.downloadImage(
      format,
      qualityValue,
      false,
      useDisplaySize,
      actualZoom,
      // PNG压缩选项
      format === 'png' && enablePngCompression ? {
        enableCompression: true,
        compressionQuality: pngCompressionQuality / 100
      } : undefined
    );
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg ${className}`}>
      <div className="flex items-center gap-2 mb-6">
        <FileImage className="w-5 h-5 text-gray-600 dark:text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          导出图片
        </h3>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label className="text-sm font-medium">
            格式
          </Label>
          <Select value={format} onValueChange={(value: 'png' | 'jpeg') => setFormat(value)}>
            <SelectTrigger className="w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="png">PNG</SelectItem>
              <SelectItem value="jpeg">JPEG</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {format === 'jpeg' && (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label className="text-sm font-medium">
                质量
              </Label>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {quality}%
              </span>
            </div>
            <input
              type="range"
              min={10}
              max={100}
              step={5}
              value={quality}
              onChange={(e) => setQuality(Number(e.target.value))}
              className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-400 dark:text-gray-500">
              <span>低</span>
              <span>高</span>
            </div>
          </div>
        )}

        {/* PNG压缩选项 */}
        {format === 'png' && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">
                启用PNG压缩
              </Label>
              <Switch
                checked={enablePngCompression}
                onCheckedChange={setEnablePngCompression}
              />
            </div>

            {enablePngCompression && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="text-sm font-medium">
                    压缩质量
                  </Label>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {pngCompressionQuality}%
                  </span>
                </div>
                <input
                  type="range"
                  min={10}
                  max={100}
                  step={5}
                  value={pngCompressionQuality}
                  onChange={(e) => setPngCompressionQuality(Number(e.target.value))}
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-xs text-gray-400 dark:text-gray-500">
                  <span>高压缩</span>
                  <span>低压缩</span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  压缩可以显著减小文件大小，但可能会轻微影响图像质量
                </p>
              </div>
            )}
          </div>
        )}

        {/* 显示尺寸导出开关 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">
              按显示尺寸导出
            </Label>
            <Switch
              checked={useDisplaySize}
              onCheckedChange={setUseDisplaySize}
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {useDisplaySize
              ? `将按当前显示尺寸导出（${Math.round(actualZoom)}%缩放）`
              : '将按图片实际尺寸导出（忽略视图缩放）'
            }
          </p>
        </div>

        <Button
          onClick={handleExport}
          disabled={disabled}
          className="w-full flex items-center gap-2"
        >
          <Download className="w-4 h-4" />
          下载图片
        </Button>

        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          {format === 'png'
            ? enablePngCompression
              ? 'PNG 格式支持透明度，已启用压缩以减小文件大小'
              : 'PNG 格式支持透明度，文件较大'
            : 'JPEG 格式文件较小，但不支持透明度'
          }
        </div>
      </div>
    </div>
  );
}
