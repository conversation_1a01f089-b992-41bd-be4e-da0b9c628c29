'use client';

import { Label } from '@/components/ui/label';

interface SharedSliderProps {
  label: string;
  value: number;
  min: number;
  max: number;
  step?: number;
  unit?: string;
  onChange: (value: number) => void;
  onChangeStart?: () => void;
  onChangeEnd?: () => void;
  size?: 'sm' | 'md';
}

export default function SharedSlider({
  label,
  value,
  min,
  max,
  step = 1,
  unit = '',
  onChange,
  onChangeStart,
  onChangeEnd,
  size = 'md'
}: SharedSliderProps) {
  const percentage = ((value - min) / (max - min)) * 100;

  const isSmall = size === 'sm';

  return (
    <div className={isSmall ? 'space-y-2 group' : 'space-y-3 group'}>
      <div className="flex justify-between items-center">
        <Label className={`${isSmall ? 'text-xs' : 'text-sm'} font-medium text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200`}>
          {label}
        </Label>
        <div className="flex items-center gap-2">
          <span className={`text-xs text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-800 ${isSmall ? 'px-1.5 py-0.5 rounded min-w-[40px]' : 'px-2 py-1 rounded-md min-w-[50px]'} text-center`}>
            {value}{unit}
          </span>
        </div>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        onMouseDown={() => onChangeStart?.()}
        onMouseUp={() => onChangeEnd?.()}
        onTouchStart={() => onChangeStart?.()}
        onTouchEnd={() => onChangeEnd?.()}
        className={`w-full ${isSmall ? 'h-1.5' : 'h-2'} bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider group-hover:bg-gray-300 dark:group-hover:bg-gray-600 transition-colors duration-200`}
        style={{
          background: `linear-gradient(to right, rgb(59 130 246) 0%, rgb(59 130 246) ${percentage}%, rgb(229 231 235) ${percentage}%, rgb(229 231 235) 100%)`
        }}
      />
    </div>
  );
}
