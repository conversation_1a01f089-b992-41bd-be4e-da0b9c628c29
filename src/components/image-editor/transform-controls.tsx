'use client';

import { RotateCw, RotateCcw, FlipHorizontal, FlipVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import SharedSlider from './shared-slider';
import { ImageFilters } from './image-canvas';

interface TransformControlsProps {
  filters: ImageFilters;
  onFiltersChange: (filters: ImageFilters, action?: string, immediate?: boolean) => void;
  className?: string;
  onDragStart?: () => void;
  onDragEnd?: () => void;
  zoom?: number;
  onZoomChange?: (zoom: number, immediate?: boolean) => void;
}

export default function TransformControls({
  filters,
  onFiltersChange,
  className = '',
  onDragStart,
  onDragEnd,
  zoom = 100,
  onZoomChange
}: TransformControlsProps) {
  const updateFilter = (key: keyof ImageFilters, value: number | boolean) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const rotateImage = (degrees: number) => {
    // 计算最接近的90度倍数
    const currentRotation = filters.rotation;
    const nearestQuarter = Math.round(currentRotation / 90) * 90;
    let newRotation = nearestQuarter + degrees;

    // 确保角度在 -180 到 180 范围内
    while (newRotation > 180) {
      newRotation -= 360;
    }
    while (newRotation < -180) {
      newRotation += 360;
    }

    updateFilter('rotation', newRotation);
  };

  const flipImage = (direction: 'horizontal' | 'vertical') => {
    if (direction === 'horizontal') {
      updateFilter('flipHorizontal', !filters.flipHorizontal);
    } else {
      updateFilter('flipVertical', !filters.flipVertical);
    }
  };



  return (
    <div className={`space-y-6 ${className}`}>
      {/* 旋转控制 */}
      <div className="space-y-4">
        <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-1">旋转</h4>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => rotateImage(-90)}
            className="flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            左转90°
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => rotateImage(90)}
            className="flex items-center gap-2"
          >
            <RotateCw className="w-4 h-4" />
            右转90°
          </Button>
        </div>

        <SharedSlider
          label="自定义角度"
          value={filters.rotation}
          min={-180}
          max={180}
          unit="°"
          onChange={(value) => updateFilter('rotation', value)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />
      </div>

      {/* 翻转控制 */}
      <div className="space-y-4">
        <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-1">翻转</h4>
        
        <div className="flex gap-2">
          <Button
            variant={filters.flipHorizontal ? "default" : "outline"}
            size="sm"
            onClick={() => flipImage('horizontal')}
            className="flex items-center gap-2"
          >
            <FlipHorizontal className="w-4 h-4" />
            水平翻转
          </Button>
          <Button
            variant={filters.flipVertical ? "default" : "outline"}
            size="sm"
            onClick={() => flipImage('vertical')}
            className="flex items-center gap-2"
          >
            <FlipVertical className="w-4 h-4" />
            垂直翻转
          </Button>
        </div>
      </div>

      {/* 视图缩放控制 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-1">视图缩放</h4>
          <div className="group relative">
            <svg className="w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="absolute bottom-full right-0 mb-2 w-64 p-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
              <div className="space-y-1">
                <div>💡 <strong>滚轮缩放：</strong></div>
                <div>• 鼠标悬停在图片上滚动滚轮</div>
                <div>• 或按住 Ctrl/Cmd + 滚轮</div>
                <div>🖱️ <strong>右键拖拽：</strong></div>
                <div>• 按住鼠标右键拖拽平移查看</div>
              </div>
            </div>
          </div>
        </div>

        {/* 快捷缩放按钮 */}
        <div className="flex gap-2 flex-wrap">
          {[50, 75, 100, 125, 150, 200].map((zoomValue) => (
            <Button
              key={zoomValue}
              variant={zoom === zoomValue ? "default" : "outline"}
              size="sm"
              onClick={() => onZoomChange?.(zoomValue, true)}
              className="h-7 px-2 text-xs"
            >
              {zoomValue}%
            </Button>
          ))}
        </div>

        <SharedSlider
          label="自定义缩放"
          value={zoom}
          min={25}
          max={200}
          step={1}
          unit="%"
          onChange={(value) => onZoomChange?.(value, false)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />
      </div>


    </div>
  );
}
