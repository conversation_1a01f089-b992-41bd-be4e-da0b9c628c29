import { CurvePoint, ColorCurves } from './image-canvas';

// 创建默认的色彩曲线（线性曲线）
export const createDefaultCurves = (): ColorCurves => ({
  rgb: [
    { x: 0, y: 0 },
    { x: 255, y: 255 }
  ],
  red: [
    { x: 0, y: 0 },
    { x: 255, y: 255 }
  ],
  green: [
    { x: 0, y: 0 },
    { x: 255, y: 255 }
  ],
  blue: [
    { x: 0, y: 0 },
    { x: 255, y: 255 }
  ]
});

// 使用三次样条插值计算曲线上的值
export const interpolateCurve = (points: CurvePoint[], x: number): number => {
  // 确保点按x坐标排序
  const sortedPoints = [...points].sort((a, b) => a.x - b.x);
  
  // 边界情况
  if (x <= sortedPoints[0].x) return sortedPoints[0].y;
  if (x >= sortedPoints[sortedPoints.length - 1].x) return sortedPoints[sortedPoints.length - 1].y;
  
  // 找到x所在的区间
  let i = 0;
  while (i < sortedPoints.length - 1 && sortedPoints[i + 1].x < x) {
    i++;
  }
  
  const p0 = sortedPoints[i];
  const p1 = sortedPoints[i + 1];
  
  // 线性插值
  const t = (x - p0.x) / (p1.x - p0.x);
  return Math.round(p0.y + t * (p1.y - p0.y));
};

// 生成完整的查找表（LUT）用于快速像素处理
export const generateCurveLUT = (points: CurvePoint[]): number[] => {
  const lut = new Array(256);
  for (let i = 0; i < 256; i++) {
    lut[i] = Math.max(0, Math.min(255, interpolateCurve(points, i)));
  }
  return lut;
};

// 生成所有通道的查找表
export const generateAllCurveLUTs = (curves: ColorCurves) => {
  return {
    rgb: generateCurveLUT(curves.rgb),
    red: generateCurveLUT(curves.red),
    green: generateCurveLUT(curves.green),
    blue: generateCurveLUT(curves.blue)
  };
};

// 使用预生成的查找表应用曲线到像素值
export const applyCurvesToPixelWithLUTs = (
  r: number,
  g: number,
  b: number,
  luts: ReturnType<typeof generateAllCurveLUTs>
): [number, number, number] => {
  // 先应用RGB主曲线
  let newR = luts.rgb[r];
  let newG = luts.rgb[g];
  let newB = luts.rgb[b];

  // 再应用各个颜色通道的曲线
  newR = luts.red[newR];
  newG = luts.green[newG];
  newB = luts.blue[newB];

  return [
    Math.max(0, Math.min(255, newR)),
    Math.max(0, Math.min(255, newG)),
    Math.max(0, Math.min(255, newB))
  ];
};

// 应用曲线到像素值（兼容性函数，性能较低）
export const applyCurvesToPixel = (
  r: number,
  g: number,
  b: number,
  curves: ColorCurves
): [number, number, number] => {
  const luts = generateAllCurveLUTs(curves);
  return applyCurvesToPixelWithLUTs(r, g, b, luts);
};

// 检查曲线是否为默认状态（线性）
export const isCurveDefault = (points: CurvePoint[]): boolean => {
  if (points.length !== 2) return false;
  return points[0].x === 0 && points[0].y === 0 && 
         points[1].x === 255 && points[1].y === 255;
};

// 检查所有曲线是否都为默认状态
export const areAllCurvesDefault = (curves: ColorCurves): boolean => {
  return isCurveDefault(curves.rgb) && 
         isCurveDefault(curves.red) && 
         isCurveDefault(curves.green) && 
         isCurveDefault(curves.blue);
};

// 添加曲线点
export const addCurvePoint = (points: CurvePoint[], x: number, y: number): CurvePoint[] => {
  const newPoints = [...points];
  
  // 检查是否已存在相同x坐标的点
  const existingIndex = newPoints.findIndex(p => Math.abs(p.x - x) < 5);
  if (existingIndex !== -1) {
    // 更新现有点
    newPoints[existingIndex] = { x, y };
  } else {
    // 添加新点
    newPoints.push({ x, y });
  }
  
  // 按x坐标排序
  return newPoints.sort((a, b) => a.x - b.x);
};

// 移除曲线点（保留端点）
export const removeCurvePoint = (points: CurvePoint[], index: number): CurvePoint[] => {
  if (index <= 0 || index >= points.length - 1) {
    return points; // 不能删除端点
  }
  
  const newPoints = [...points];
  newPoints.splice(index, 1);
  return newPoints;
};

// 重置曲线到默认状态
export const resetCurve = (): CurvePoint[] => [
  { x: 0, y: 0 },
  { x: 255, y: 255 }
];

// 预设曲线
export const CURVE_PRESETS = {
  // 增强对比度
  contrast: {
    rgb: [
      { x: 0, y: 0 },
      { x: 64, y: 32 },
      { x: 192, y: 224 },
      { x: 255, y: 255 }
    ]
  },
  
  // 柔和曲线
  soft: {
    rgb: [
      { x: 0, y: 0 },
      { x: 64, y: 80 },
      { x: 192, y: 176 },
      { x: 255, y: 255 }
    ]
  },
  
  // 高光压制
  highlightCompress: {
    rgb: [
      { x: 0, y: 0 },
      { x: 128, y: 128 },
      { x: 192, y: 200 },
      { x: 255, y: 240 }
    ]
  },
  
  // 阴影提升
  shadowLift: {
    rgb: [
      { x: 0, y: 15 },
      { x: 64, y: 80 },
      { x: 128, y: 128 },
      { x: 255, y: 255 }
    ]
  }
};
