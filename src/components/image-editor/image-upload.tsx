'use client';

import { useCallback, useState } from 'react';
import { Upload, Image as ImageIcon } from 'lucide-react';

interface ImageUploadProps {
  onImageUpload: (file: File) => void;
  className?: string;
}

export default function ImageUpload({ onImageUpload, className = '' }: ImageUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validateFile = (file: File): boolean => {
    const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (!validTypes.includes(file.type)) {
      setError('不支持的文件格式。支持 JPG、PNG、WebP 和 GIF 格式。');
      return false;
    }

    if (file.size > maxSize) {
      setError('文件大小超过 50MB 限制。');
      return false;
    }

    setError(null);
    return true;
  };

  const handleFileSelect = useCallback((file: File) => {
    if (validateFile(file)) {
      onImageUpload(file);
    }
  }, [onImageUpload]);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  return (
    <div className={`w-full ${className}`}>
      <div
        className={`
          relative border-2 border-dashed rounded-3xl p-16 text-center transition-all duration-500 group cursor-pointer overflow-hidden
          ${isDragOver
            ? 'border-blue-500 bg-gradient-to-br from-blue-50 via-blue-100/50 to-indigo-50 dark:from-blue-950/40 dark:via-blue-900/30 dark:to-indigo-950/20 scale-105 shadow-2xl'
            : 'border-slate-300 dark:border-slate-600 hover:border-blue-400 dark:hover:border-blue-500 hover:bg-gradient-to-br hover:from-slate-50 hover:via-blue-50/30 hover:to-indigo-50/30 dark:hover:from-slate-800/50 dark:hover:via-slate-700/50 dark:hover:to-slate-900/50 hover:scale-102 hover:shadow-xl'
          }
          ${error ? 'border-red-400 bg-gradient-to-br from-red-50 via-red-100/50 to-pink-50 dark:from-red-950/40 dark:via-red-900/30 dark:to-pink-950/20' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />

        <div className="flex flex-col items-center space-y-8">
          {/* 图标区域 - 增强设计 */}
          <div className={`
            relative p-8 rounded-3xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3
            ${error
              ? 'bg-gradient-to-br from-red-100 via-red-200 to-pink-100 dark:from-red-900/40 dark:via-red-800/30 dark:to-pink-900/20'
              : isDragOver
                ? 'bg-gradient-to-br from-blue-100 via-blue-200 to-indigo-100 dark:from-blue-900/40 dark:via-blue-800/30 dark:to-indigo-900/20'
                : 'bg-gradient-to-br from-slate-100 via-slate-200 to-blue-100 dark:from-slate-800 dark:via-slate-700 dark:to-slate-600'
            }
            shadow-lg group-hover:shadow-2xl
          `}>
            {error ? (
              <ImageIcon className="w-16 h-16 text-red-500 transition-all duration-300" />
            ) : (
              <Upload className={`w-16 h-16 transition-all duration-500 ${
                isDragOver
                  ? 'text-blue-600 scale-110 rotate-12'
                  : 'text-slate-500 dark:text-slate-400 group-hover:text-blue-500 group-hover:scale-110'
              }`} />
            )}

            {/* 增强的装饰性光晕 */}
            <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-indigo-500/30 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
            <div className="absolute -inset-2 bg-gradient-to-r from-white/40 to-white/20 dark:from-slate-700/40 dark:to-slate-800/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            {/* 脉冲动画 */}
            <div className={`absolute inset-0 rounded-3xl transition-all duration-1000 ${
              isDragOver ? 'animate-pulse bg-blue-400/20' : ''
            }`}></div>
          </div>

          {/* 文字内容 - 增强设计 */}
          <div className="space-y-3">
            <h3 className={`text-2xl font-bold transition-all duration-300 ${
              error
                ? 'text-red-600 dark:text-red-400'
                : 'text-slate-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 group-hover:scale-105'
            }`}>
              {error || '拖拽图片到这里或点击上传'}
            </h3>
            <p className="text-slate-600 dark:text-slate-400 text-lg leading-relaxed">
              {error ? '请重试上传' : '支持 JPG、PNG、WebP 和 GIF 格式'}
            </p>
          </div>

          {/* 格式信息 - 增强设计 */}
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-slate-100/80 to-white/80 dark:from-slate-800/80 dark:to-slate-700/80 rounded-2xl border border-slate-200/50 dark:border-slate-600/50 shadow-sm backdrop-blur-sm">
            <span className="text-sm text-slate-600 dark:text-slate-400 font-medium">
              支持格式：JPG, PNG, WebP, GIF • 最大 50MB
            </span>
          </div>

          {/* 快捷提示 - 增强设计 */}
          {!error && (
            <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-slate-500 dark:text-slate-400">
              <div className="flex items-center gap-2 group/tip">
                <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full group-hover/tip:scale-125 transition-transform duration-200"></div>
                <span className="group-hover/tip:text-blue-500 transition-colors duration-200">拖拽上传</span>
              </div>
              <div className="flex items-center gap-2 group/tip">
                <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full group-hover/tip:scale-125 transition-transform duration-200"></div>
                <span className="group-hover/tip:text-green-500 transition-colors duration-200">点击选择</span>
              </div>
              <div className="flex items-center gap-2 group/tip">
                <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full group-hover/tip:scale-125 transition-transform duration-200"></div>
                <span className="group-hover/tip:text-purple-500 transition-colors duration-200">即时预览</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
