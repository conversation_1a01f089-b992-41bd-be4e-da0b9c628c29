'use client';

import { useState } from 'react';
import { Palette, Move, Sliders, RotateCcw, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import UnifiedImageControls from '@/components/image-editor/unified-image-controls';
import TransformControls from './transform-controls';
import ColorCurves from './color-curves';

import { ImageFilters, ImageCanvasRef } from './image-canvas';
import { createDefaultCurves } from './curve-utils';

interface ControlTabsProps {
  filters: ImageFilters;
  onFiltersChange: (filters: ImageFilters, action?: string, immediate?: boolean) => void;
  canvasRef: React.RefObject<ImageCanvasRef | null>;
  image?: HTMLImageElement | null;
  disabled?: boolean;
  className?: string;
  onDragStart?: () => void;
  onDragEnd?: () => void;
  zoom?: number;
  onZoomChange?: (zoom: number, immediate?: boolean) => void;
}

type TabType = 'basic' | 'advanced' | 'curves' | 'transform';

interface Tab {
  id: TabType;
  label: string;
  icon: React.ReactNode;
}

const tabs: Tab[] = [
  {
    id: 'basic',
    label: '基础调整',
    icon: <Sliders className="w-4 h-4" />
  },
  {
    id: 'advanced',
    label: '专业调色',
    icon: <Palette className="w-4 h-4" />
  },
  {
    id: 'curves',
    label: '色彩曲线',
    icon: <TrendingUp className="w-4 h-4" />
  },
  {
    id: 'transform',
    label: '几何变换',
    icon: <Move className="w-4 h-4" />
  }
];

export default function ControlTabs({
  filters,
  onFiltersChange,
  canvasRef,
  image,
  disabled = false,
  className = '',
  onDragStart,
  onDragEnd,
  zoom = 100,
  onZoomChange
}: ControlTabsProps) {
  const [activeTab, setActiveTab] = useState<TabType>('basic');

  const resetAllFilters = () => {
    const defaultFilters: ImageFilters = {
      // 基础调整
      brightness: 100,
      contrast: 100,
      saturation: 100,
      hue: 0,
      blur: 0,
      sepia: 0,
      grayscale: 0,

      // 高级色彩调整
      exposure: 0,
      highlights: 0,
      shadows: 0,
      whites: 0,
      blacks: 0,
      temperature: 0,
      tint: 0,
      vibrance: 0,

      // 色彩曲线
      curves: createDefaultCurves(),

      // 视图控制
      zoom: 100,

      // 几何变换
      rotation: 0,
      flipHorizontal: false,
      flipVertical: false,

      // 高级滤镜
      sharpen: 0,
      noise: 0,
      vignette: 0,
      clarity: 0
    };

    // 检查当前滤镜是否已经是默认值
    const isAlreadyDefault = Object.keys(defaultFilters).every(key => {
      return filters[key as keyof ImageFilters] === defaultFilters[key as keyof typeof defaultFilters];
    });

    // 如果滤镜已经是默认值，不执行重置
    if (isAlreadyDefault) {
      return;
    }

    // 重置所有滤镜（包括zoom）
    onFiltersChange(defaultFilters, 'reset_all', true);
  };



  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return (
          <UnifiedImageControls
            filters={filters}
            onFiltersChange={onFiltersChange}
            mode="basic"
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
          />
        );
      case 'advanced':
        return (
          <UnifiedImageControls
            filters={filters}
            onFiltersChange={onFiltersChange}
            mode="advanced"
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
          />
        );
      case 'curves':
        return (
          <ColorCurves
            curves={filters.curves}
            onCurvesChange={(curves) => onFiltersChange({ ...filters, curves }, 'curves')}
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
          />
        );
      case 'transform':
        return (
          <TransformControls
            filters={filters}
            onFiltersChange={onFiltersChange}
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
            zoom={zoom}
            onZoomChange={onZoomChange}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={`h-full flex flex-col bg-white dark:bg-gray-900 ${className}`}>
      {/* 标签页头部 - 现代化设计 */}
      <div className="flex bg-gray-50/50 dark:bg-gray-800/50 p-1 m-4 rounded-xl border border-gray-200/50 dark:border-gray-700/50">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`
              flex-1 flex flex-col items-center justify-center gap-1.5 px-3 py-2.5 text-xs font-medium transition-all duration-300 rounded-lg
              ${activeTab === tab.id
                ? 'text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 shadow-sm border border-gray-200/50 dark:border-gray-700/50'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-white/50 dark:hover:bg-gray-800/50'
              }
            `}
          >
            <div className={`transition-transform duration-300 ${activeTab === tab.id ? 'scale-110' : ''}`}>
              {tab.icon}
            </div>
            <span className="text-[10px] font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* 标签页内容 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 标题栏 */}
        <div className="flex justify-between items-center px-6 py-3 border-b border-gray-100 dark:border-gray-800">
          <div>
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              {tabs.find(tab => tab.id === activeTab)?.label}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
              {activeTab === 'basic' && '亮度、对比度、饱和度等基础参数'}
              {activeTab === 'advanced' && '曝光、高光阴影、色温色调、锐化暗角等专业调色'}
              {activeTab === 'curves' && 'RGB和单色通道曲线调整，精确控制色调映射'}
              {activeTab === 'transform' && '旋转、翻转、缩放等几何变换'}
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={resetAllFilters}
            className="h-8 px-3 text-xs border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            重置
          </Button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto px-6 py-4" style={{ maxHeight: 'calc(100vh - 220px)' }}>
          <div className="space-y-6">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
}
