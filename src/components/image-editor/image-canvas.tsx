'use client';

import { useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { compressPNGFromCanvas } from '@/utils/png-compression';
import { generateAllCurveLUTs, applyCurvesToPixelWithLUTs, areAllCurvesDefault } from './curve-utils';

// 色彩曲线点的接口
export interface CurvePoint {
  x: number; // 输入值 (0-255)
  y: number; // 输出值 (0-255)
}

// 色彩曲线数据接口
export interface ColorCurves {
  rgb: CurvePoint[];    // RGB主曲线
  red: CurvePoint[];    // 红色通道曲线
  green: CurvePoint[];  // 绿色通道曲线
  blue: CurvePoint[];   // 蓝色通道曲线
}

export interface ImageFilters {
  // 基础调整
  brightness: number;
  contrast: number;
  saturation: number;
  hue: number;
  blur: number;
  sepia: number;
  grayscale: number;

  // 高级色彩调整
  exposure: number;        // 曝光度
  highlights: number;      // 高光
  shadows: number;         // 阴影
  whites: number;          // 白色色阶
  blacks: number;          // 黑色色阶
  temperature: number;     // 色温
  tint: number;           // 色调
  vibrance: number;       // 自然饱和度

  // 色彩曲线
  curves: ColorCurves;    // 色彩曲线数据

  // 视图控制
  zoom: number;           // 视图缩放比例

  // 几何变换
  rotation: number;        // 旋转角度
  flipHorizontal: boolean; // 水平翻转
  flipVertical: boolean;   // 垂直翻转

  // 高级滤镜
  sharpen: number;        // 锐化
  noise: number;          // 噪点
  vignette: number;       // 暗角
  clarity: number;        // 清晰度
}

interface ImageCanvasProps {
  image: HTMLImageElement | null;
  filters: ImageFilters;
  className?: string;
  onZoomChange?: (zoom: number, immediate?: boolean) => void;
}

interface PngCompressionOptions {
  enableCompression: boolean;
  compressionQuality: number;
}

export interface ImageCanvasRef {
  getCanvas: () => HTMLCanvasElement | null;
  getImageData: () => ImageData | null;
  downloadImage: (format: 'png' | 'jpeg', quality?: number, cropToContent?: boolean, useDisplaySize?: boolean, zoom?: number, pngOptions?: PngCompressionOptions) => Promise<void>;
}

// 应用高级效果的独立函数
const applyAdvancedEffectsToCanvas = (ctx: CanvasRenderingContext2D, filters: ImageFilters) => {
  const canvas = ctx.canvas;
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  // 预生成曲线查找表以提高性能
  const curveLUTs = !areAllCurvesDefault(filters.curves)
    ? generateAllCurveLUTs(filters.curves)
    : null;

  for (let i = 0; i < data.length; i += 4) {
    let r = data[i];
    let g = data[i + 1];
    let b = data[i + 2];

    // 首先应用色彩曲线调整
    if (curveLUTs) {
      [r, g, b] = applyCurvesToPixelWithLUTs(r, g, b, curveLUTs);
    }

    // 应用色温调整
    if (filters.temperature !== 0) {
      const temp = filters.temperature / 100;
      if (temp > 0) {
        // 暖色调
        r = Math.min(255, r + temp * 30);
        b = Math.max(0, b - temp * 20);
      } else {
        // 冷色调
        r = Math.max(0, r + temp * 20);
        b = Math.min(255, b - temp * 30);
      }
    }

    // 应用色调调整
    if (filters.tint !== 0) {
      const tint = filters.tint / 100;
      if (tint > 0) {
        g = Math.min(255, g + tint * 20);
        r = Math.max(0, r - tint * 10);
      } else {
        g = Math.max(0, g + tint * 20);
        r = Math.min(255, r - tint * 10);
      }
    }

    // 应用高光/阴影调整
    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
    const isHighlight = luminance > 128;

    if (isHighlight && filters.highlights !== 0) {
      const factor = 1 + (filters.highlights / 100);
      r = Math.min(255, Math.max(0, r * factor));
      g = Math.min(255, Math.max(0, g * factor));
      b = Math.min(255, Math.max(0, b * factor));
    } else if (!isHighlight && filters.shadows !== 0) {
      const factor = 1 + (filters.shadows / 100);
      r = Math.min(255, Math.max(0, r * factor));
      g = Math.min(255, Math.max(0, g * factor));
      b = Math.min(255, Math.max(0, b * factor));
    }

    // 应用白色/黑色色阶
    if (filters.whites !== 0) {
      const whiteFactor = 1 + (filters.whites / 100);
      if (luminance > 200) {
        r = Math.min(255, r * whiteFactor);
        g = Math.min(255, g * whiteFactor);
        b = Math.min(255, b * whiteFactor);
      }
    }

    if (filters.blacks !== 0) {
      const blackFactor = 1 + (filters.blacks / 100);
      if (luminance < 55) {
        r = Math.max(0, r * blackFactor);
        g = Math.max(0, g * blackFactor);
        b = Math.max(0, b * blackFactor);
      }
    }

    // 应用锐化
    if (filters.sharpen > 0) {
      const sharpenFactor = filters.sharpen / 100;
      r = Math.min(255, Math.max(0, r + (r - 128) * sharpenFactor));
      g = Math.min(255, Math.max(0, g + (g - 128) * sharpenFactor));
      b = Math.min(255, Math.max(0, b + (b - 128) * sharpenFactor));
    }

    // 应用噪点
    if (filters.noise > 0) {
      const noiseFactor = filters.noise / 100;
      const noise = (Math.random() - 0.5) * noiseFactor * 50;
      r = Math.min(255, Math.max(0, r + noise));
      g = Math.min(255, Math.max(0, g + noise));
      b = Math.min(255, Math.max(0, b + noise));
    }

    data[i] = r;
    data[i + 1] = g;
    data[i + 2] = b;
  }

  // 应用暗角效果
  if (filters.vignette > 0) {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);

    for (let y = 0; y < canvas.height; y++) {
      for (let x = 0; x < canvas.width; x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        const vignetteFactor = 1 - (distance / maxDistance) * (filters.vignette / 100);

        const index = (y * canvas.width + x) * 4;
        data[index] *= vignetteFactor;     // R
        data[index + 1] *= vignetteFactor; // G
        data[index + 2] *= vignetteFactor; // B
      }
    }
  }

  ctx.putImageData(imageData, 0, 0);
};

// 创建裁剪到内容的画布 - 使用更简单直接的方法
const createCroppedCanvas = (originalCanvas: HTMLCanvasElement, image: HTMLImageElement, filters: ImageFilters) => {
  // 如果没有进行几何变换，直接返回原画布
  if (filters.rotation === 0 && !filters.flipHorizontal && !filters.flipVertical) {
    return originalCanvas;
  }

  // 创建一个足够大的临时画布来容纳任何可能的变换
  const tempCanvas = document.createElement('canvas');
  const maxDimension = Math.max(originalCanvas.width, originalCanvas.height);
  tempCanvas.width = maxDimension * 3;
  tempCanvas.height = maxDimension * 3;

  const tempCtx = tempCanvas.getContext('2d');
  if (!tempCtx) return originalCanvas;

  // 清除临时画布
  tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);

  // 保存状态
  tempCtx.save();

  // 移动到临时画布中心
  tempCtx.translate(tempCanvas.width / 2, tempCanvas.height / 2);

  // 应用变换（与原始applyFilters逻辑完全一致）
  if (filters.rotation !== 0) {
    tempCtx.rotate((filters.rotation * Math.PI) / 180);
  }

  const scaleX = filters.flipHorizontal ? -1 : 1;
  const scaleY = filters.flipVertical ? -1 : 1;
  tempCtx.scale(scaleX, scaleY);

  // 应用滤镜
  const adjustedBrightness = filters.brightness + (filters.exposure * 20);
  const adjustedContrast = filters.contrast + (filters.clarity * 10);

  const filterString = [
    `brightness(${Math.max(0, adjustedBrightness)}%)`,
    `contrast(${Math.max(0, adjustedContrast)}%)`,
    `saturate(${Math.max(0, filters.saturation + filters.vibrance)}%)`,
    `hue-rotate(${filters.hue}deg)`,
    `blur(${filters.blur}px)`,
    `sepia(${filters.sepia}%)`,
    `grayscale(${filters.grayscale}%)`
  ].join(' ');

  tempCtx.filter = filterString;

  // 绘制图像 - 使用原始图像尺寸
  tempCtx.drawImage(image, -image.width / 2, -image.height / 2, image.width, image.height);

  // 恢复状态
  tempCtx.restore();

  // 应用高级效果（需要像素级处理）
  applyAdvancedEffectsToCanvas(tempCtx, filters);

  // 现在找到实际内容的边界
  const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
  const data = imageData.data;

  let minX = tempCanvas.width, maxX = 0, minY = tempCanvas.height, maxY = 0;
  let hasContent = false;

  // 扫描像素找到非透明区域
  for (let y = 0; y < tempCanvas.height; y++) {
    for (let x = 0; x < tempCanvas.width; x++) {
      const alpha = data[(y * tempCanvas.width + x) * 4 + 3];
      if (alpha > 0) { // 非透明像素
        hasContent = true;
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);
      }
    }
  }

  // 如果没有找到内容，返回原画布
  if (!hasContent) {
    return originalCanvas;
  }

  // 创建裁剪后的画布
  const croppedWidth = maxX - minX + 1;
  const croppedHeight = maxY - minY + 1;

  const croppedCanvas = document.createElement('canvas');
  croppedCanvas.width = croppedWidth;
  croppedCanvas.height = croppedHeight;

  const croppedCtx = croppedCanvas.getContext('2d');
  if (!croppedCtx) return originalCanvas;

  // 复制裁剪区域到新画布
  croppedCtx.drawImage(
    tempCanvas,
    minX, minY, croppedWidth, croppedHeight,
    0, 0, croppedWidth, croppedHeight
  );

  return croppedCanvas;
};

// 创建原始尺寸的画布
const createOriginalSizeCanvas = (image: HTMLImageElement, filters: ImageFilters) => {
  // 计算变换后需要的画布尺寸
  const rotatedBounds = calculateRotatedBounds(
    image.width,
    image.height,
    filters.rotation
  );

  let canvasWidth = rotatedBounds.width;
  let canvasHeight = rotatedBounds.height;
  let imageScaleFactor = 1;

  // 检查画布尺寸是否安全
  if (!isCanvasSizeSafe(rotatedBounds.width, rotatedBounds.height)) {
    console.warn('原始画布尺寸过大，将限制到安全范围');

    // 计算安全的缩放因子
    imageScaleFactor = Math.min(
      MAX_CANVAS_SIZE / rotatedBounds.width,
      MAX_CANVAS_SIZE / rotatedBounds.height,
      Math.sqrt(MAX_CANVAS_AREA / (rotatedBounds.width * rotatedBounds.height))
    );

    canvasWidth = Math.floor(rotatedBounds.width * imageScaleFactor);
    canvasHeight = Math.floor(rotatedBounds.height * imageScaleFactor);
  }

  const canvas = document.createElement('canvas');
  canvas.width = canvasWidth;
  canvas.height = canvasHeight;

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.error('无法创建canvas上下文');
    return canvas;
  }

  try {
    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 保存状态
    ctx.save();

    // 移动到画布中心
    ctx.translate(canvas.width / 2, canvas.height / 2);

    // 应用变换
    if (filters.rotation !== 0) {
      ctx.rotate((filters.rotation * Math.PI) / 180);
    }

    const scaleX = filters.flipHorizontal ? -1 : 1;
    const scaleY = filters.flipVertical ? -1 : 1;
    ctx.scale(scaleX, scaleY);

    // 应用滤镜
    const adjustedBrightness = filters.brightness + (filters.exposure * 20);
    const adjustedContrast = filters.contrast + (filters.clarity * 10);

    const filterString = [
      `brightness(${Math.max(0, adjustedBrightness)}%)`,
      `contrast(${Math.max(0, adjustedContrast)}%)`,
      `saturate(${Math.max(0, filters.saturation + filters.vibrance)}%)`,
      `hue-rotate(${filters.hue}deg)`,
      `blur(${filters.blur}px)`,
      `sepia(${filters.sepia}%)`,
      `grayscale(${filters.grayscale}%)`
    ].join(' ');

    ctx.filter = filterString;

    // 绘制图像 - 根据缩放因子调整图像尺寸
    const drawWidth = image.width * imageScaleFactor;
    const drawHeight = image.height * imageScaleFactor;
    ctx.drawImage(image, -drawWidth / 2, -drawHeight / 2, drawWidth, drawHeight);

    // 恢复状态
    ctx.restore();

    // 应用高级效果
    applyAdvancedEffectsToCanvas(ctx, filters);

  } catch (error) {
    console.error('创建原始尺寸画布时出错:', error);
  }

  return canvas;
};

// Canvas尺寸限制常量
const MAX_CANVAS_SIZE = 16384; // 安全的最大canvas尺寸
const MAX_CANVAS_AREA = 268435456; // 最大canvas面积 (16384 * 16384)

// 检查canvas尺寸是否安全
const isCanvasSizeSafe = (width: number, height: number): boolean => {
  return width > 0 && height > 0 &&
         width <= MAX_CANVAS_SIZE &&
         height <= MAX_CANVAS_SIZE &&
         width * height <= MAX_CANVAS_AREA;
};

// 计算安全的缩放因子
const calculateSafeZoomFactor = (originalWidth: number, originalHeight: number, requestedZoom: number): number => {
  // 确保输入值有效
  if (originalWidth <= 0 || originalHeight <= 0 || requestedZoom <= 0) {
    return 1;
  }

  const zoomFactor = requestedZoom / 100;
  const targetWidth = originalWidth * zoomFactor;
  const targetHeight = originalHeight * zoomFactor;

  if (isCanvasSizeSafe(targetWidth, targetHeight)) {
    return zoomFactor;
  }

  // 计算安全的最大缩放因子
  const maxZoomByWidth = originalWidth > 0 ? MAX_CANVAS_SIZE / originalWidth : 1;
  const maxZoomByHeight = originalHeight > 0 ? MAX_CANVAS_SIZE / originalHeight : 1;
  const maxZoomByArea = Math.sqrt(MAX_CANVAS_AREA / (originalWidth * originalHeight));

  const safeZoom = Math.min(maxZoomByWidth, maxZoomByHeight, maxZoomByArea, zoomFactor);
  return Math.max(0.01, safeZoom); // 确保最小缩放为1%
};

// 创建显示尺寸的画布（应用zoom）
const createDisplaySizeCanvas = (image: HTMLImageElement, filters: ImageFilters, zoom: number) => {
  // 先创建原始尺寸的画布
  const originalCanvas = createOriginalSizeCanvas(image, filters);

  // 计算安全的缩放因子
  const safeZoomFactor = calculateSafeZoomFactor(originalCanvas.width, originalCanvas.height, zoom);
  const displayWidth = Math.round(originalCanvas.width * safeZoomFactor);
  const displayHeight = Math.round(originalCanvas.height * safeZoomFactor);

  // 如果请求的缩放超出安全范围，在控制台警告
  if (safeZoomFactor < zoom / 100) {
    console.warn(`缩放过大，已自动调整到安全范围。请求缩放: ${zoom}%, 实际缩放: ${Math.round(safeZoomFactor * 100)}%`);
  }

  // 检查最终尺寸是否安全
  if (!isCanvasSizeSafe(displayWidth, displayHeight)) {
    console.error('Canvas尺寸超出安全范围，返回原始尺寸画布');
    return originalCanvas;
  }

  // 创建显示尺寸的画布
  const displayCanvas = document.createElement('canvas');
  displayCanvas.width = displayWidth;
  displayCanvas.height = displayHeight;

  const ctx = displayCanvas.getContext('2d');
  if (!ctx) {
    console.error('无法创建canvas上下文，返回原始尺寸画布');
    return originalCanvas;
  }

  try {
    // 将原始画布缩放绘制到显示画布上
    ctx.drawImage(originalCanvas, 0, 0, displayWidth, displayHeight);
  } catch (error) {
    console.error('绘制缩放画布时出错:', error);
    return originalCanvas;
  }

  return displayCanvas;
};

// 计算旋转后的边界框尺寸
const calculateRotatedBounds = (width: number, height: number, rotation: number) => {
  // 将角度转换为弧度
  const radians = (rotation * Math.PI) / 180;

  // 计算旋转后的边界框
  const cos = Math.abs(Math.cos(radians));
  const sin = Math.abs(Math.sin(radians));

  const rotatedWidth = width * cos + height * sin;
  const rotatedHeight = width * sin + height * cos;

  return {
    width: Math.ceil(rotatedWidth),
    height: Math.ceil(rotatedHeight)
  };
};

const ImageCanvas = forwardRef<ImageCanvasRef, ImageCanvasProps>(
  ({ image, filters, className = '', onZoomChange }, ref) => {
    const zoom = filters.zoom;
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const originalImageRef = useRef<HTMLImageElement | null>(null);
    const isDraggingRef = useRef(false);
    const lastMousePosRef = useRef({ x: 0, y: 0 });

    useImperativeHandle(ref, () => ({
      getCanvas: () => canvasRef.current,
      getImageData: () => {
        const canvas = canvasRef.current;
        if (!canvas) return null;
        const ctx = canvas.getContext('2d');
        if (!ctx) return null;
        return ctx.getImageData(0, 0, canvas.width, canvas.height);
      },
      downloadImage: async (format: 'png' | 'jpeg', quality = 0.9, cropToContent = false, useDisplaySize = false, zoomValue = 100, pngOptions?: PngCompressionOptions) => {
        const canvas = canvasRef.current;
        if (!canvas || !image) return;

        try {
          let exportCanvas: HTMLCanvasElement;

          if (useDisplaySize) {
            // 使用显示尺寸（应用zoom）
            exportCanvas = createDisplaySizeCanvas(image, filters, zoomValue);
          } else {
            // 使用原始尺寸
            exportCanvas = createOriginalSizeCanvas(image, filters);
          }

          // 如果启用裁剪到内容，对画布进行裁剪
          if (cropToContent) {
            exportCanvas = createCroppedCanvas(exportCanvas, image, filters);
          }

          // 检查导出画布的尺寸
          if (!isCanvasSizeSafe(exportCanvas.width, exportCanvas.height)) {
            console.error('导出画布尺寸过大，无法安全导出');

            // 计算建议的最大缩放比例
            const rotatedBounds = calculateRotatedBounds(image.width, image.height, filters.rotation);
            const maxSafeZoom = Math.floor(calculateSafeZoomFactor(rotatedBounds.width, rotatedBounds.height, 100) * 100);

            alert(`图片尺寸过大，无法导出。建议将缩放比例降低到 ${maxSafeZoom}% 以下后重试。`);
            return;
          }

          const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';

          // 处理PNG压缩
          if (format === 'png' && pngOptions?.enableCompression) {
            try {
              const compressedBlob = await compressPNGFromCanvas(exportCanvas, {
                quality: pngOptions.compressionQuality,
                noCompressIfLarger: true
              });

              // 创建下载链接
              const url = URL.createObjectURL(compressedBlob);
              const link = document.createElement('a');
              link.download = `edited-image.${format}`;
              link.href = url;

              // 触发下载
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // 清理URL对象
              URL.revokeObjectURL(url);
              return;
            } catch (error) {
              console.error('PNG压缩失败，使用原始方法:', error);
              // 如果压缩失败，继续使用原始方法
            }
          }

          // 原始方法：转换为DataURL
          let dataURL: string;
          try {
            dataURL = exportCanvas.toDataURL(mimeType, quality);
          } catch (error) {
            console.error('转换图片数据时出错:', error);
            alert('图片处理失败，可能是因为图片过大。请降低缩放比例后重试。');
            return;
          }

          // 检查DataURL是否有效
          if (!dataURL || dataURL === 'data:,') {
            console.error('生成的图片数据无效');
            alert('图片生成失败，请降低缩放比例后重试。');
            return;
          }

          // 创建下载链接
          const link = document.createElement('a');
          link.download = `edited-image.${format}`;
          link.href = dataURL;

          // 触发下载
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

        } catch (error) {
          console.error('下载图片时出错:', error);
          alert('下载失败，请降低缩放比例后重试。');
        }
      }
    }));

    const applyFilters = (ctx: CanvasRenderingContext2D, img: HTMLImageElement) => {
      const canvas = ctx.canvas;

      // 计算图像的显示尺寸（考虑最大尺寸限制）
      const maxWidth = 800;
      const maxHeight = 600;
      let { width, height } = img;

      // Scale to fit base dimensions while maintaining aspect ratio
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // 计算实际的缩放因子（考虑画布安全限制）
      const rotatedBounds = calculateRotatedBounds(width, height, filters.rotation);
      const requestedZoomFactor = zoom / 100;
      const actualZoomFactor = Math.min(
        requestedZoomFactor,
        canvas.width / rotatedBounds.width,
        canvas.height / rotatedBounds.height
      );

      const imageWidth = width * actualZoomFactor;
      const imageHeight = height * actualZoomFactor;

      // 保存当前状态
      ctx.save();

      // 应用几何变换
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;

      ctx.translate(centerX, centerY);

      // 应用旋转
      if (filters.rotation !== 0) {
        ctx.rotate((filters.rotation * Math.PI) / 180);
      }

      // 应用翻转
      const scaleX = filters.flipHorizontal ? -1 : 1;
      const scaleY = filters.flipVertical ? -1 : 1;
      ctx.scale(scaleX, scaleY);

      // 计算调整后的亮度和对比度（考虑曝光度）
      const adjustedBrightness = filters.brightness + (filters.exposure * 20);
      const adjustedContrast = filters.contrast + (filters.clarity * 10);

      // 色温和色调调整在像素级处理中实现

      // 应用CSS滤镜
      const filterString = [
        `brightness(${Math.max(0, adjustedBrightness)}%)`,
        `contrast(${Math.max(0, adjustedContrast)}%)`,
        `saturate(${Math.max(0, filters.saturation + filters.vibrance)}%)`,
        `hue-rotate(${filters.hue}deg)`,
        `blur(${filters.blur}px)`,
        `sepia(${filters.sepia}%)`,
        `grayscale(${filters.grayscale}%)`
      ].join(' ');

      ctx.filter = filterString;

      // 绘制图像 - 使用正确的图像尺寸
      ctx.drawImage(img, -imageWidth / 2, -imageHeight / 2, imageWidth, imageHeight);

      // 恢复状态
      ctx.restore();

      // 应用高级效果（需要像素级处理）
      applyAdvancedEffects(ctx);
    };

    const applyAdvancedEffects = (ctx: CanvasRenderingContext2D) => {
      const canvas = ctx.canvas;
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // 预生成曲线查找表以提高性能
      const curveLUTs = !areAllCurvesDefault(filters.curves)
        ? generateAllCurveLUTs(filters.curves)
        : null;

      for (let i = 0; i < data.length; i += 4) {
        let r = data[i];
        let g = data[i + 1];
        let b = data[i + 2];

        // 首先应用色彩曲线调整
        if (curveLUTs) {
          [r, g, b] = applyCurvesToPixelWithLUTs(r, g, b, curveLUTs);
        }

        // 应用色温调整
        if (filters.temperature !== 0) {
          const temp = filters.temperature / 100;
          if (temp > 0) {
            // 暖色调
            r = Math.min(255, r + temp * 30);
            b = Math.max(0, b - temp * 20);
          } else {
            // 冷色调
            r = Math.max(0, r + temp * 20);
            b = Math.min(255, b - temp * 30);
          }
        }

        // 应用色调调整
        if (filters.tint !== 0) {
          const tint = filters.tint / 100;
          if (tint > 0) {
            g = Math.min(255, g + tint * 20);
            r = Math.max(0, r - tint * 10);
          } else {
            g = Math.max(0, g + tint * 20);
            r = Math.min(255, r - tint * 10);
          }
        }

        // 应用高光/阴影调整
        const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
        const isHighlight = luminance > 128;

        if (isHighlight && filters.highlights !== 0) {
          const factor = 1 + (filters.highlights / 100);
          r = Math.min(255, Math.max(0, r * factor));
          g = Math.min(255, Math.max(0, g * factor));
          b = Math.min(255, Math.max(0, b * factor));
        } else if (!isHighlight && filters.shadows !== 0) {
          const factor = 1 + (filters.shadows / 100);
          r = Math.min(255, Math.max(0, r * factor));
          g = Math.min(255, Math.max(0, g * factor));
          b = Math.min(255, Math.max(0, b * factor));
        }

        // 应用白色/黑色色阶
        if (filters.whites !== 0) {
          const whiteFactor = 1 + (filters.whites / 100);
          if (luminance > 200) {
            r = Math.min(255, r * whiteFactor);
            g = Math.min(255, g * whiteFactor);
            b = Math.min(255, b * whiteFactor);
          }
        }

        if (filters.blacks !== 0) {
          const blackFactor = 1 + (filters.blacks / 100);
          if (luminance < 55) {
            r = Math.max(0, r * blackFactor);
            g = Math.max(0, g * blackFactor);
            b = Math.max(0, b * blackFactor);
          }
        }

        // 应用锐化
        if (filters.sharpen > 0) {
          // 简单的锐化算法
          const sharpenFactor = filters.sharpen / 100;
          r = Math.min(255, Math.max(0, r + (r - 128) * sharpenFactor));
          g = Math.min(255, Math.max(0, g + (g - 128) * sharpenFactor));
          b = Math.min(255, Math.max(0, b + (b - 128) * sharpenFactor));
        }

        // 应用噪点
        if (filters.noise > 0) {
          const noiseAmount = filters.noise / 100 * 50;
          const noise = (Math.random() - 0.5) * noiseAmount;
          r = Math.min(255, Math.max(0, r + noise));
          g = Math.min(255, Math.max(0, g + noise));
          b = Math.min(255, Math.max(0, b + noise));
        }

        data[i] = r;
        data[i + 1] = g;
        data[i + 2] = b;
      }

      // 应用暗角效果
      if (filters.vignette > 0) {
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);

        for (let y = 0; y < canvas.height; y++) {
          for (let x = 0; x < canvas.width; x++) {
            const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
            const vignetteFactor = 1 - (distance / maxDistance) * (filters.vignette / 100);

            const index = (y * canvas.width + x) * 4;
            data[index] *= vignetteFactor;     // R
            data[index + 1] *= vignetteFactor; // G
            data[index + 2] *= vignetteFactor; // B
          }
        }
      }

      ctx.putImageData(imageData, 0, 0);
    };

    const drawImage = () => {
      const canvas = canvasRef.current;
      const ctx = canvas?.getContext('2d');

      if (!canvas || !ctx || !image) return;

      // Use fixed base dimensions to avoid layout-dependent calculations
      const maxWidth = 800;
      const maxHeight = 600;

      let { width, height } = image;

      // Scale to fit base dimensions while maintaining aspect ratio
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // 计算旋转后的边界框
      const rotatedBounds = calculateRotatedBounds(
        width,
        height,
        filters.rotation
      );

      // Apply zoom factor to the rotated bounds
      const zoomFactor = zoom / 100;
      let finalWidth = Math.round(rotatedBounds.width * zoomFactor);
      let finalHeight = Math.round(rotatedBounds.height * zoomFactor);

      // 确保画布尺寸在安全范围内
      if (!isCanvasSizeSafe(finalWidth, finalHeight)) {
        console.warn('画布尺寸过大，自动调整到安全范围');
        const safeZoomFactor = calculateSafeZoomFactor(rotatedBounds.width, rotatedBounds.height, zoom);
        finalWidth = Math.round(rotatedBounds.width * safeZoomFactor);
        finalHeight = Math.round(rotatedBounds.height * safeZoomFactor);
      }

      // 确保最小尺寸
      finalWidth = Math.max(1, finalWidth);
      finalHeight = Math.max(1, finalHeight);

      canvas.width = finalWidth;
      canvas.height = finalHeight;

      // Clear canvas
      ctx.clearRect(0, 0, finalWidth, finalHeight);

      // Apply filters and draw image
      applyFilters(ctx, image);
    };

    useEffect(() => {
      if (image && image !== originalImageRef.current) {
        originalImageRef.current = image;
        drawImage();
      }
    }, [image]);

    useEffect(() => {
      if (image) {
        drawImage();
      }
    }, [filters, image, zoom]);

    // 鼠标滚轮缩放功能
    useEffect(() => {
      const canvas = canvasRef.current;
      if (!canvas || !onZoomChange) return;

      const handleWheel = (e: WheelEvent) => {
        // 检查是否按住了 Ctrl 键或 Meta 键（Mac 的 Cmd 键）
        // 这样可以避免与浏览器的默认缩放冲突
        if (e.ctrlKey || e.metaKey) {
          // 阻止浏览器的默认缩放行为
          e.preventDefault();
          e.stopPropagation();

          // 计算缩放变化量 - 使用更平滑的缩放
          const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1; // 向下滚动缩小，向上滚动放大

          // 计算新的缩放值
          const newZoom = Math.max(25, Math.min(200, Math.round(zoom * zoomFactor))); // 限制缩放范围在25%-200%之间

          // 只有当缩放值真正改变时才触发更新
          if (newZoom !== zoom) {
            onZoomChange(newZoom, true);
          }
          return;
        }

        // 如果没有按住修饰键，检查鼠标是否在画布上
        const rect = canvas.getBoundingClientRect();
        const isMouseOverCanvas =
          e.clientX >= rect.left &&
          e.clientX <= rect.right &&
          e.clientY >= rect.top &&
          e.clientY <= rect.bottom;

        // 只有当鼠标在画布上且没有按住修饰键时，才进行画布缩放
        if (isMouseOverCanvas) {
          // 阻止页面滚动
          e.preventDefault();
          e.stopPropagation();

          // 计算缩放变化量
          const zoomStep = 5; // 更小的步长，更平滑
          const delta = e.deltaY > 0 ? -zoomStep : zoomStep;

          // 计算新的缩放值
          const newZoom = Math.max(25, Math.min(200, zoom + delta));

          // 只有当缩放值真正改变时才触发更新
          if (newZoom !== zoom) {
            onZoomChange(newZoom, true);
          }
        }
        // 如果鼠标不在画布上，允许正常的页面滚动
      };

      // 添加事件监听器到整个文档，这样可以捕获 Ctrl+滚轮的组合
      document.addEventListener('wheel', handleWheel, { passive: false });

      return () => {
        document.removeEventListener('wheel', handleWheel);
      };
    }, [zoom, onZoomChange]);

    // 鼠标右键拖拽平移功能
    useEffect(() => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const scrollContainer = canvas.closest('.overflow-auto') as HTMLElement;
      if (!scrollContainer) return;

      const handleMouseDown = (e: MouseEvent) => {
        // 只处理右键点击
        if (e.button === 2) {
          e.preventDefault();
          isDraggingRef.current = true;
          lastMousePosRef.current = { x: e.clientX, y: e.clientY };

          // 改变鼠标样式
          document.body.style.cursor = 'grabbing';

          // 阻止右键菜单
          e.stopPropagation();
        }
      };

      const handleMouseMove = (e: MouseEvent) => {
        if (!isDraggingRef.current) return;

        e.preventDefault();

        const deltaX = e.clientX - lastMousePosRef.current.x;
        const deltaY = e.clientY - lastMousePosRef.current.y;

        // 更新滚动位置（注意方向相反，拖拽向右时内容向左移动）
        scrollContainer.scrollLeft -= deltaX;
        scrollContainer.scrollTop -= deltaY;

        lastMousePosRef.current = { x: e.clientX, y: e.clientY };
      };

      const handleMouseUp = (e: MouseEvent) => {
        if (e.button === 2 && isDraggingRef.current) {
          isDraggingRef.current = false;
          document.body.style.cursor = '';
        }
      };

      const handleContextMenu = (e: MouseEvent) => {
        // 阻止右键菜单显示
        e.preventDefault();
      };

      // 添加事件监听器
      canvas.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      canvas.addEventListener('contextmenu', handleContextMenu);

      return () => {
        canvas.removeEventListener('mousedown', handleMouseDown);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        canvas.removeEventListener('contextmenu', handleContextMenu);

        // 清理鼠标样式
        document.body.style.cursor = '';
      };
    }, []);

    return (
      <div className={`bg-gray-50 dark:bg-gray-900 rounded-lg ${className}`}>
        <canvas
          ref={canvasRef}
          className="border border-gray-200 dark:border-gray-700 rounded shadow-lg"
          style={{
            display: 'block',
            cursor: zoom > 100 ? 'grab' : 'default'
          }}
          title="滚轮缩放：鼠标悬停在图片上滚动滚轮进行缩放，或按住 Ctrl/Cmd + 滚轮进行缩放&#10;右键拖拽：按住鼠标右键拖拽可以平移查看图片"
        />
      </div>
    );
  }
);

ImageCanvas.displayName = 'ImageCanvas';

export default ImageCanvas;
