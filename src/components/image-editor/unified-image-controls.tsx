'use client';

import SharedSlider from './shared-slider';
import { ImageFilters } from './image-canvas';

interface UnifiedImageControlsProps {
  filters: ImageFilters;
  onFiltersChange: (filters: ImageFilters, action?: string, immediate?: boolean) => void;
  mode: 'basic' | 'advanced';
  className?: string;
  onDragStart?: () => void;
  onDragEnd?: () => void;
}

export default function UnifiedImageControls({
  filters,
  onFiltersChange,
  mode,
  className = '',
  onDragStart,
  onDragEnd
}: UnifiedImageControlsProps) {
  const updateFilter = (key: keyof ImageFilters, value: number) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  // 创建带有拖拽事件的滑块组件
  const createSlider = (props: any) => (
    <SharedSlider
      {...props}
      onChangeStart={onDragStart}
      onChangeEnd={onDragEnd}
    />
  );

  if (mode === 'basic') {
    return (
      <div className={`space-y-6 ${className}`}>
        <SharedSlider
          label="亮度"
          value={filters.brightness}
          min={0}
          max={200}
          unit="%"
          onChange={(value) => updateFilter('brightness', value)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />

        <SharedSlider
          label="对比度"
          value={filters.contrast}
          min={0}
          max={200}
          unit="%"
          onChange={(value) => updateFilter('contrast', value)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />

        <SharedSlider
          label="饱和度"
          value={filters.saturation}
          min={0}
          max={200}
          unit="%"
          onChange={(value) => updateFilter('saturation', value)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />

        <SharedSlider
          label="色调"
          value={filters.hue}
          min={-180}
          max={180}
          unit="°"
          onChange={(value) => updateFilter('hue', value)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />

        <SharedSlider
          label="模糊"
          value={filters.blur}
          min={0}
          max={10}
          step={0.1}
          unit="px"
          onChange={(value) => updateFilter('blur', value)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />

        <SharedSlider
          label="怀旧"
          value={filters.sepia}
          min={0}
          max={100}
          unit="%"
          onChange={(value) => updateFilter('sepia', value)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />

        <SharedSlider
          label="灰度"
          value={filters.grayscale}
          min={0}
          max={100}
          unit="%"
          onChange={(value) => updateFilter('grayscale', value)}
          onChangeStart={onDragStart}
          onChangeEnd={onDragEnd}
        />
      </div>
    );
  }

  // Advanced mode
  return (
    <div className={`space-y-4 ${className}`}>
      {/* 曝光调整 */}
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-1">
          曝光调整
        </h4>
        <div className="grid grid-cols-1 gap-3">
          {createSlider({
            label: "曝光度",
            value: filters.exposure,
            min: -100,
            max: 100,
            size: "sm",
            onChange: (value: number) => updateFilter('exposure', value)
          })}
          <div className="grid grid-cols-2 gap-3">
            {createSlider({
              label: "高光",
              value: filters.highlights,
              min: -100,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('highlights', value)
            })}
            {createSlider({
              label: "阴影",
              value: filters.shadows,
              min: -100,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('shadows', value)
            })}
          </div>
          <div className="grid grid-cols-2 gap-3">
            {createSlider({
              label: "白色色阶",
              value: filters.whites,
              min: -100,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('whites', value)
            })}
            {createSlider({
              label: "黑色色阶",
              value: filters.blacks,
              min: -100,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('blacks', value)
            })}
          </div>
        </div>
      </div>

      {/* 色彩调整 */}
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-1">
          色彩调整
        </h4>
        <div className="grid grid-cols-1 gap-3">
          <div className="grid grid-cols-2 gap-3">
            {createSlider({
              label: "色温",
              value: filters.temperature,
              min: -100,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('temperature', value)
            })}
            {createSlider({
              label: "色调偏移",
              value: filters.tint,
              min: -100,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('tint', value)
            })}
          </div>
          <div className="grid grid-cols-2 gap-3">
            {createSlider({
              label: "自然饱和度",
              value: filters.vibrance,
              min: -100,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('vibrance', value)
            })}
            {createSlider({
              label: "清晰度",
              value: filters.clarity,
              min: -100,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('clarity', value)
            })}
          </div>
        </div>
      </div>

      {/* 艺术效果 */}
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-1">
          艺术效果
        </h4>
        <div className="grid grid-cols-1 gap-3">
          <div className="grid grid-cols-2 gap-3">
            {createSlider({
              label: "锐化",
              value: filters.sharpen,
              min: 0,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('sharpen', value)
            })}
            {createSlider({
              label: "噪点",
              value: filters.noise,
              min: 0,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('noise', value)
            })}
          </div>
          <div className="grid grid-cols-2 gap-3">
            {createSlider({
              label: "暗角",
              value: filters.vignette,
              min: 0,
              max: 100,
              size: "sm",
              onChange: (value: number) => updateFilter('vignette', value)
            })}
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-1">
          使用说明
        </h4>
        <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1.5 leading-relaxed">
          <p>• <strong>曝光度</strong>：整体亮度调整，模拟相机曝光</p>
          <p>• <strong>高光/阴影</strong>：分别调整亮部和暗部细节</p>
          <p>• <strong>色温</strong>：调整图像冷暖色调</p>
          <p>• <strong>锐化</strong>：增强图像边缘细节</p>
          <p>• <strong>暗角</strong>：边缘变暗，突出中心主体</p>
        </div>
      </div>
    </div>
  );
}
