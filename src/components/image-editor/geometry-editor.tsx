'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import ImageUpload from './image-upload';
import ImageCanvas, { ImageCanvasRef, ImageFilters } from './image-canvas';
import { createDefaultCurves } from './curve-utils';
import TransformControls from './transform-controls';
import { useHistory } from '@/hooks/use-history';
import Toolbar from './toolbar';

export default function GeometryEditor() {
  const canvasRef = useRef<ImageCanvasRef>(null);
  const [image, setImage] = useState<HTMLImageElement | null>(null);

  const defaultFilters: ImageFilters = {
    // 基础调整
    brightness: 100,
    contrast: 100,
    saturation: 100,
    hue: 0,
    blur: 0,
    sepia: 0,
    grayscale: 0,

    // 高级色彩调整
    exposure: 0,
    highlights: 0,
    shadows: 0,
    whites: 0,
    blacks: 0,
    temperature: 0,
    tint: 0,
    vibrance: 0,

    // 色彩曲线
    curves: createDefaultCurves(),

    // 视图控制
    zoom: 100,

    // 几何变换
    rotation: 0,
    flipHorizontal: false,
    flipVertical: false,

    // 高级滤镜（保持占位，便于与画布类型兼容）
    sharpen: 0,
    noise: 0,
    vignette: 0,
    clarity: 0
  };

  const {
    currentState: filters,
    canUndo,
    canRedo,
    pushState,
    startDragging,
    endDragging,
    undo,
    redo,
    reset: resetHistory,
    cleanup
  } = useHistory(defaultFilters, {
    maxHistorySize: 50,
    debounceMs: 300
  });

  const validateFile = useCallback((file: File): { isValid: boolean; error?: string } => {
    const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (!validTypes.includes(file.type)) {
      return { isValid: false, error: '不支持的文件格式。支持 JPG、PNG、WebP 和 GIF 格式。' };
    }

    if (file.size > maxSize) {
      return { isValid: false, error: '文件大小超过 50MB 限制。' };
    }

    return { isValid: true };
  }, []);

  const handleImageUpload = useCallback((file: File) => {
    const validation = validateFile(file);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      setImage(img);
      resetHistory();
      URL.revokeObjectURL(url);
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      console.error('Failed to load image');
      alert('图片加载失败，请检查文件是否损坏。');
    };

    img.src = url;
  }, [resetHistory, validateFile]);

  const handleFiltersChange = useCallback((newFilters: ImageFilters, action?: string, immediate = false) => {
    pushState(newFilters, action, immediate);
  }, [pushState]);

  const handleZoomChange = useCallback((newZoom: number, immediate = false) => {
    handleFiltersChange({ ...filters, zoom: newZoom }, 'zoom_change', immediate);
  }, [filters, handleFiltersChange]);

  // 键盘快捷键（撤销/重做）
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'z':
            if (e.shiftKey) {
              e.preventDefault();
              redo();
            } else {
              e.preventDefault();
              undo();
            }
            break;
          case 'y':
            e.preventDefault();
            redo();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [undo, redo]);

  // 清理
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50 dark:from-slate-900 dark:via-slate-900 dark:to-indigo-950/50 flex flex-col relative overflow-hidden">
      {/* 背景装饰与网格，与主编辑器保持一致风格 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-blue-400/15 to-purple-600/15 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '4s' }}></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-tr from-indigo-400/15 to-pink-600/15 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '5s', animationDelay: '1s' }}></div>
        <div className="absolute top-1/3 left-10 w-32 h-32 bg-gradient-to-br from-emerald-400/12 to-teal-600/12 rounded-full blur-2xl animate-bounce" style={{ animationDuration: '6s' }}></div>
        <div className="absolute bottom-1/3 right-10 w-40 h-40 bg-gradient-to-br from-rose-400/12 to-orange-600/12 rounded-full blur-2xl animate-bounce" style={{ animationDuration: '7s', animationDelay: '2s' }}></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.1)_1px,transparent_0)] dark:bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.03)_1px,transparent_0)] bg-[size:32px_32px] opacity-40"></div>
      </div>

      {/* 工具栏：包含下载/分享/导出设置等 */}
      <Toolbar
        canvasRef={canvasRef}
        onReset={() => resetHistory()}
        disabled={!image}
        canUndo={canUndo}
        canRedo={canRedo}
        onUndo={undo}
        onRedo={redo}
        image={image}
        filters={filters}
        zoom={filters.zoom}
        onImageUpload={handleImageUpload}
      />

      {/* 主体内容区域 */}
      <div className="flex-1 flex min-h-0 relative gap-6">
        {/* 左侧画布区或上传提示 */}
        <div className="flex-1 flex flex-col min-w-0">
          <div
            className={`flex-1 p-8 ${image ? 'overflow-auto' : 'overflow-visible'}`}
            style={image ? { maxHeight: 'calc(100vh - 200px)' } : undefined}
          >
            <div className={`min-h-full ${image ? 'flex items-center' : ''}`}>
              {!image ? (
                <div className="w-full max-w-xl mx-auto">
                  <div className="flex flex-col items-center justify-center gap-6 py-20">
                    <div className="text-center">
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">几何变换</h2>
                      <p className="text-sm text-gray-600 dark:text-gray-300">上传一张图片后进行旋转、翻转与缩放</p>
                    </div>
                    <ImageUpload onImageUpload={handleImageUpload} />
                  </div>
                </div>
              ) : (
                <div className="relative group w-fit mx-auto">
                  <ImageCanvas
                    ref={canvasRef}
                    image={image}
                    filters={filters}
                    onZoomChange={handleZoomChange}
                    className="max-w-none shadow-2xl transition-all duration-500 group-hover:shadow-3xl rounded-2xl overflow-hidden"
                  />

                  {/* 装饰效果，与主编辑器一致 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 rounded-3xl -z-10 blur-3xl opacity-50 group-hover:opacity-70 transition-all duration-500 animate-pulse" style={{ animationDuration: '3s' }}></div>
                  <div className="absolute -inset-4 bg-gradient-to-r from-cyan-400/15 via-blue-500/15 to-violet-500/15 rounded-2xl -z-10 blur-xl opacity-60 group-hover:opacity-80 transition-all duration-300"></div>
                  <div className="absolute -inset-2 bg-gradient-to-r from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-900/40 rounded-xl -z-10 backdrop-blur-sm border border-white/20 dark:border-slate-700/20"></div>
                  <div className="absolute -top-3 -left-3 w-6 h-6 border-t-3 border-l-3 border-blue-400/70 rounded-tl-xl transition-all duration-300 group-hover:border-blue-500 group-hover:scale-110"></div>
                  <div className="absolute -top-3 -right-3 w-6 h-6 border-t-3 border-r-3 border-purple-400/70 rounded-tr-xl transition-all duration-300 group-hover:border-purple-500 group-hover:scale-110"></div>
                  <div className="absolute -bottom-3 -left-3 w-6 h-6 border-b-3 border-l-3 border-indigo-400/70 rounded-bl-xl transition-all duration-300 group-hover:border-indigo-500 group-hover:scale-110"></div>
                  <div className="absolute -bottom-3 -right-3 w-6 h-6 border-b-3 border-r-3 border-violet-400/70 rounded-br-xl transition-all duration-300 group-hover:border-violet-500 group-hover:scale-110"></div>
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-blue-400/60 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-16 bg-gradient-to-b from-transparent via-indigo-400/60 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-1 h-16 bg-gradient-to-b from-transparent via-violet-400/60 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 仅几何变换侧边栏（有图片时显示） */}
        {image && (
          <div
            className="hidden lg:flex flex-col flex-shrink-0 w-96 min-h-0 border-l border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-b from-white/95 via-white/90 to-slate-50/95 dark:from-slate-900/95 dark:via-slate-900/90 dark:to-slate-800/95 backdrop-blur-sm relative p-4 overflow-y-auto"
            style={{ maxHeight: 'calc(100vh - 200px)' }}
          >
            {/* 侧边栏装饰 */}
            <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-500 via-purple-500 to-indigo-500 opacity-60"></div>
            <TransformControls
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onDragStart={startDragging}
              onDragEnd={endDragging}
              zoom={filters.zoom}
              onZoomChange={handleZoomChange}
            />
          </div>
        )}
      </div>
    </div>
  );
}
