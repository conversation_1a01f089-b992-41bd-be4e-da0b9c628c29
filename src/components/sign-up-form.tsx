'use client'

import { cn } from '@/lib/utils'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

export function SignUpForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [repeatPassword, setRepeatPassword] = useState('')
  const [otp, setOtp] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [showOtpStep, setShowOtpStep] = useState(false)
  const router = useRouter()

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    const supabase = createClient()
    setIsLoading(true)
    setError(null)

    if (password !== repeatPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    try {
      const { error } = await supabase.auth.signUp({
        email,
        password
      })
      if (error) throw error
      // 显示OTP验证步骤
      setShowOtpStep(true)
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault()
    const supabase = createClient()
    setIsVerifying(true)
    setError(null)

    if (otp.length !== 6) {
      setError('Please enter a 6-digit verification code')
      setIsVerifying(false)
      return
    }

    try {
      const { error } = await supabase.auth.verifyOtp({
        email,
        token: otp,
        type: 'signup'
      })

      if (error) throw error
      router.push('/')
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Invalid verification code')
    } finally {
      setIsVerifying(false)
    }
  }

  const handleResendOtp = async () => {
    const supabase = createClient()
    setIsResending(true)
    setError(null)

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email
      })
      if (error) throw error
      setError(null)
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to resend code')
    } finally {
      setIsResending(false)
    }
  }

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className='text-2xl'>
            {showOtpStep ? 'Verify Your Email' : 'Sign up'}
          </CardTitle>
          <CardDescription>
            {showOtpStep
              ? 'Enter the 6-digit code sent to your email to complete registration'
              : 'Create a new account'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {showOtpStep ? (
            <form onSubmit={handleVerifyOtp}>
              <div className='flex flex-col gap-6'>
                <div className='grid gap-2'>
                  <Label htmlFor='email'>Email</Label>
                  <Input id='email' type='email' value={email} disabled className='bg-muted' />
                </div>
                <div className='grid gap-2'>
                  <Label htmlFor='otp'>Verification Code</Label>
                  <Input
                    id='otp'
                    type='text'
                    placeholder='Enter 6-digit code'
                    required
                    value={otp}
                    onChange={e => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 6)
                      setOtp(value)
                    }}
                    maxLength={6}
                    className='text-center text-lg tracking-widest'
                  />
                </div>
                {error && <p className='text-sm text-red-500'>{error}</p>}
                <Button type='submit' className='w-full' disabled={isVerifying}>
                  {isVerifying ? 'Verifying...' : 'Verify Code'}
                </Button>
                <Button
                  type='button'
                  variant='outline'
                  className='w-full'
                  disabled={isResending}
                  onClick={handleResendOtp}
                >
                  {isResending ? 'Resending...' : 'Resend Code'}
                </Button>
              </div>
              <div className='mt-4 text-center text-sm'>
                <button
                  type='button'
                  onClick={() => setShowOtpStep(false)}
                  className='underline underline-offset-4'
                >
                  Back to Sign up
                </button>
              </div>
            </form>
          ) : (
            <form onSubmit={handleSignUp}>
              <div className='flex flex-col gap-6'>
                <div className='grid gap-2'>
                  <Label htmlFor='email'>Email</Label>
                  <Input
                    id='email'
                    type='email'
                    placeholder='<EMAIL>'
                    required
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                  />
                </div>
                <div className='grid gap-2'>
                  <div className='flex items-center'>
                    <Label htmlFor='password'>Password</Label>
                  </div>
                  <Input
                    id='password'
                    type='password'
                    required
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                  />
                </div>
                <div className='grid gap-2'>
                  <div className='flex items-center'>
                    <Label htmlFor='repeat-password'>Repeat Password</Label>
                  </div>
                  <Input
                    id='repeat-password'
                    type='password'
                    required
                    value={repeatPassword}
                    onChange={e => setRepeatPassword(e.target.value)}
                  />
                </div>
                {error && <p className='text-sm text-red-500'>{error}</p>}
                <Button type='submit' className='w-full' disabled={isLoading}>
                  {isLoading ? 'Creating an account...' : 'Sign up'}
                </Button>
              </div>
              <div className='mt-4 text-center text-sm'>
                Already have an account?{' '}
                <Link href='/auth/login' className='underline underline-offset-4'>
                  Login
                </Link>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
