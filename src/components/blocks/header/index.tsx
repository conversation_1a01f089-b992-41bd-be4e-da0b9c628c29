'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

import { CircleCheckIcon, CircleHelpIcon, CircleIcon, Menu, LogOut, User } from 'lucide-react'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle
} from '@/components/ui/navigation-menu'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

import { Header as HeaderType } from '@/types/blocks/header'
import Icon from '@/components/icon'
import Link from 'next/link'
import LocaleToggle from '@/components/locale/toggle'
import ModeToggle from '@/components/theme/theme-toggle'
import { useAuth } from '@/hooks/use-auth'

function ListItem({
  title,
  children,
  href,
  ...props
}: React.ComponentPropsWithoutRef<'li'> & { href: string }) {
  return (
    <li {...props}>
      <NavigationMenuLink asChild>
        <Link href={href}>
          <div className='text-sm leading-none font-medium'>{title}</div>
          {children && (
            <p className='text-muted-foreground line-clamp-2 text-sm leading-snug'>{children}</p>
          )}
        </Link>
      </NavigationMenuLink>
    </li>
  )
}

const IconMap = {
  CircleHelpIcon: <CircleHelpIcon />,
  CircleIcon: <CircleIcon />,
  CircleCheckIcon: <CircleCheckIcon />
}

// 添加类型定义
type IconMapKeys = keyof typeof IconMap

export default function Header({ header }: { header: HeaderType }) {
  const { user, signOut, loading } = useAuth()

  if (header.disabled) {
    return null
  }

  const navItems = header.nav?.items?.map(item => {
    if (item.children) {
      if (item.children_column && item.children_column === 2) {
        return {
          ...item,
          content: (
            <ul className='grid w-[400px] gap-2 md:w-[500px] md:grid-cols-2 lg:w-[600px]'>
              {item.children.map(child => (
                <ListItem key={child.title} title={child.title} href={child.href || ''}>
                  {child.description}
                </ListItem>
              ))}
            </ul>
          )
        }
      } else if (item.show_children_icon) {
        return {
          ...item,
          content: (
            <ul className='grid w-[200px] gap-4'>
              <li>
                {item.children.map(child => (
                  <NavigationMenuLink asChild key={child.title}>
                    <Link href={child.href || ''} className='flex-row items-center gap-2'>
                      {child.icon && IconMap[child.icon as IconMapKeys]}
                      {child.title}
                    </Link>
                  </NavigationMenuLink>
                ))}
              </li>
            </ul>
          )
        }
      } else {
        return {
          ...item,
          content: (
            <ul className='grid w-[300px] gap-4'>
              <li>
                {item.children.map(child => (
                  <NavigationMenuLink asChild key={child.title}>
                    <Link href={child.href || ''}>
                      <div className='font-medium'>{child.title}</div>
                      {child.description && (
                        <div className='text-muted-foreground'>{child.description}</div>
                      )}
                    </Link>
                  </NavigationMenuLink>
                ))}
              </li>
            </ul>
          )
        }
      }
    } else {
      return item
    }
  })

  return (
    <header className='py-3 relative z-50'>
      <div className='md:max-w-7xl mx-auto px-4'>
        <nav className='hidden justify-between lg:flex'>
          <div className='flex items-center gap-6'>
            <Link href={header.brand?.href || ''} className='flex items-center gap-2'>
              {header.brand?.logo?.src && (
                <img
                  src={header.brand.logo.src}
                  alt={header.brand.logo.alt || header.brand.title}
                  className='w-8'
                />
              )}
              {header.brand?.title && (
                <span className='text-xl text-primary font-bold'>{header.brand?.title || ''}</span>
              )}
            </Link>
            <div className='flex items-center'>
              <NavigationMenu viewport={false}>
                <NavigationMenuList>
                  {navItems!.map((item, index) => (
                    <NavigationMenuItem key={index}>
                      {item.href ? (
                        <NavigationMenuLink asChild className={navigationMenuTriggerStyle()}>
                          <Link href={item.href || '#'}>{item.title}</Link>
                        </NavigationMenuLink>
                      ) : (
                        <>
                          <NavigationMenuTrigger>{item.title}</NavigationMenuTrigger>
                          <NavigationMenuContent>{item.content}</NavigationMenuContent>
                        </>
                      )}
                    </NavigationMenuItem>
                  ))}
                </NavigationMenuList>
              </NavigationMenu>
            </div>
          </div>
          <div className='shrink-0 flex gap-2 items-center'>
            {header.show_locale && <LocaleToggle />}
            {header.show_theme && <ModeToggle />}

            {header.buttons?.map((item, i) => {
              return (
                <Button key={i} variant={item.variant}>
                  <Link
                    href={item.href || ''}
                    target={item.target || ''}
                    className='flex items-center gap-1'
                  >
                    {item.title}
                    {item.icon && <Icon name={item.icon} className='size-4 shrink-0' />}
                  </Link>
                </Button>
              )
            })}
            {header.show_sign && (
              <>
                {loading ? (
                  <div className='size-8 animate-pulse bg-muted rounded-full' />
                ) : !!user ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Avatar className='cursor-pointer'>
                        <AvatarImage
                          src={user?.avatar_url || undefined}
                          alt={user?.email || 'User'}
                        />
                        <AvatarFallback>
                          {user?.email?.charAt(0).toUpperCase() || <User className='size-4' />}
                        </AvatarFallback>
                      </Avatar>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align='end'>
                      <DropdownMenuLabel>我的账户</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={signOut}>
                        <LogOut className='mr-2 size-4' />
                        退出登录
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Button asChild>
                    <Link href='/auth/login'>登录</Link>
                  </Button>
                )}
              </>
            )}
          </div>
        </nav>

        {/* mobile */}
        <nav className='lg:hidden flex justify-between items-center'>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='outline'>
                <Menu className='size-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='w-56' align='start'>
              <DropdownMenuLabel>导航菜单</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {header.nav?.items?.map((item, index) => {
                if (item.children) {
                  return (
                    <DropdownMenuSub key={index}>
                      <DropdownMenuSubTrigger>{item.title}</DropdownMenuSubTrigger>
                      <DropdownMenuPortal>
                        <DropdownMenuSubContent>
                          {item.children.map((child, index) => {
                            return (
                              <DropdownMenuItem key={index} asChild>
                                <Link href={child.href || '#'}>{child.title}</Link>
                              </DropdownMenuItem>
                            )
                          })}
                        </DropdownMenuSubContent>
                      </DropdownMenuPortal>
                    </DropdownMenuSub>
                  )
                } else {
                  return (
                    <DropdownMenuItem key={index} asChild>
                      <Link href={item.href || '#'}>{item.title}</Link>
                    </DropdownMenuItem>
                  )
                }
              })}
            </DropdownMenuContent>
          </DropdownMenu>

          <Link href={header.brand?.href || ''} className='flex items-center gap-2'>
            {header.brand?.logo?.src && (
              <img
                src={header.brand.logo.src}
                alt={header.brand.logo.alt || header.brand.title}
                className='w-8'
              />
            )}
            {header.brand?.title && (
              <span className='text-xl text-primary font-bold'>{header.brand?.title || ''}</span>
            )}
          </Link>

          {/* 移动端用户认证 */}
          {header.show_sign && (
            <div className='flex items-center gap-2'>
              {header.show_locale && <LocaleToggle />}
              {header.show_theme && <ModeToggle />}
              {loading ? (
                <div className='size-8 animate-pulse bg-muted rounded-full' />
              ) : !!user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Avatar className='cursor-pointer'>
                      <AvatarImage
                        src={user?.avatar_url || undefined}
                        alt={user?.email || 'User'}
                      />
                      <AvatarFallback>
                        {user?.email?.charAt(0).toUpperCase() || <User className='size-4' />}
                      </AvatarFallback>
                    </Avatar>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align='end'>
                    <DropdownMenuLabel>我的账户</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={signOut}>
                      <LogOut className='mr-2 size-4' />
                      退出登录
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Button asChild size='sm'>
                  <Link href='/auth/login'>登录</Link>
                </Button>
              )}
            </div>
          )}
        </nav>
      </div>
    </header>
  )
}
