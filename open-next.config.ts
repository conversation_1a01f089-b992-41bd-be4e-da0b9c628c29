import { defineCloudflareConfig } from "@opennextjs/cloudflare";
import staticAssetsIncrementalCache from "@opennextjs/cloudflare/overrides/incremental-cache/static-assets-incremental-cache";
// import r2IncrementalCache from "@opennextjs/cloudflare/overrides/incremental-cache/r2-incremental-cache";
// import { withRegionalCache } from "@opennextjs/cloudflare/overrides/incremental-cache/regional-cache";
export default defineCloudflareConfig({
  // Uncomment to enable R2 cache,
  // It should be imported as:
  // `import r2IncrementalCache from "@opennextjs/cloudflare/overrides/incremental-cache/r2-incremental-cache";`
  // See https://opennext.js.org/cloudflare/caching for more details
  // incrementalCache: r2IncrementalCache,
  incrementalCache: staticAssetsIncrementalCache,
  // incrementalCache: withRegionalCache(r2IncrementalCache, {
  //   mode: "long-lived",
  //   shouldLazilyUpdateOnCacheHit: true,
  // }),
  enableCacheInterception: true,
});
