
# 使用ssg时的注意事项 (https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering)

1. 由于我们使用动态路由段作为 [locale] 参数，我们需要通过 generateStaticParams 将所有可能的值传递给 Next.js，以便在构建时渲染路由。

```js
import {routing} from '@/i18n/routing';
 
export function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}
```

2. 确保在调用 next-intl 的任何函数（如 useTranslations 或 getMessages ）之前，需要先调用 setRequestLocale(locale)。   