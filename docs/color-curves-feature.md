# 色彩曲线功能说明

## 功能概述

色彩曲线是专业图像编辑中最重要的调色工具之一，现已成功集成到图像编辑器中。此功能提供了精确的色调映射控制，允许用户对图像的亮度、对比度和颜色进行细致调整。

## 主要特性

### 1. 多通道曲线支持
- **RGB主曲线**: 同时影响所有颜色通道的整体色调
- **红色通道**: 独立调整红色分量
- **绿色通道**: 独立调整绿色分量  
- **蓝色通道**: 独立调整蓝色分量

### 2. 交互式曲线编辑
- **点击添加**: 在曲线上点击任意位置添加控制点
- **拖拽调整**: 拖拽控制点精确调整曲线形状
- **双击删除**: 双击控制点删除（端点除外）
- **实时预览**: 调整过程中实时显示效果

### 3. 预设曲线
- **增强对比**: 增强图像对比度的S型曲线
- **柔和曲线**: 降低对比度的反S型曲线
- **压制高光**: 压制过亮区域，保留细节
- **提升阴影**: 提亮暗部区域，增强细节

### 4. 性能优化
- **查找表(LUT)**: 使用预计算的查找表提高处理速度
- **批量处理**: 优化的像素级处理算法
- **实时响应**: 流畅的交互体验

## 使用方法

### 基本操作
1. 在图像编辑器中上传图片
2. 点击"色彩曲线"标签页
3. 选择要调整的通道（RGB/红/绿/蓝）
4. 在曲线图上点击添加控制点
5. 拖拽控制点调整曲线形状
6. 观察实时预览效果

### 高级技巧
- **对比度调整**: 创建S型曲线增强对比度
- **色彩校正**: 使用单色通道曲线修正色偏
- **创意调色**: 结合多通道创造独特的色彩效果
- **细节恢复**: 使用阴影/高光曲线恢复细节

## 技术实现

### 核心算法
- **三次样条插值**: 平滑的曲线插值算法
- **双线性映射**: RGB主曲线 + 单色通道曲线的组合应用
- **像素级处理**: 精确的颜色值映射

### 性能特性
- **O(1)查找**: 预计算的256级查找表
- **内存优化**: 高效的数据结构设计
- **实时处理**: 优化的渲染管道

## 文件结构

```
src/components/image-editor/
├── color-curves.tsx      # 曲线UI组件
├── curve-utils.ts        # 曲线处理工具函数
├── image-canvas.tsx      # 图像渲染和处理
└── control-tabs.tsx      # 控制面板集成
```

## API接口

### CurvePoint
```typescript
interface CurvePoint {
  x: number; // 输入值 (0-255)
  y: number; // 输出值 (0-255)
}
```

### ColorCurves
```typescript
interface ColorCurves {
  rgb: CurvePoint[];    // RGB主曲线
  red: CurvePoint[];    // 红色通道曲线
  green: CurvePoint[];  // 绿色通道曲线
  blue: CurvePoint[];   // 蓝色通道曲线
}
```

### 主要函数
- `generateCurveLUT()`: 生成查找表
- `applyCurvesToPixelWithLUTs()`: 应用曲线到像素
- `interpolateCurve()`: 曲线插值计算
- `addCurvePoint()`: 添加曲线控制点

## 未来扩展

### 计划中的功能
1. **曲线预设管理**: 保存和加载自定义曲线预设
2. **直方图集成**: 在曲线编辑器中显示实时直方图
3. **色彩空间支持**: 支持LAB、HSV等色彩空间的曲线
4. **批量应用**: 将曲线设置应用到多张图片

### 性能优化
1. **WebGL加速**: 使用GPU加速像素处理
2. **Web Workers**: 后台线程处理大图像
3. **增量更新**: 只处理变化的区域

## 总结

色彩曲线功能为图像编辑器带来了专业级的调色能力，通过直观的交互界面和高性能的处理算法，用户可以实现精确的色彩控制和创意调色效果。这是迈向专业图像编辑软件的重要一步。
