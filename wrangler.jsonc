/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "lenskiss",	
	"main": ".open-next/worker.js",
	"compatibility_date": "2025-06-17",
	"compatibility_flags": [
		"nodejs_compat",
		"global_fetch_strictly_public" // 希望 Worker 的内部请求也能像外部请求一样
	],
	"assets": {
		"binding": "ASSETS",
		"directory": ".open-next/assets"
	},
	"observability": {
		"enabled": true  // defalut value: true
	},
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },

	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */
	//  "r2_buckets": [
	// 	{
	// 		"binding": "NEXT_INC_CACHE_R2_BUCKET",
	// 		"bucket_name": "lenskiss"
	// 	}
	//  ],
	 "services": [
		{
			"binding": "WORKER_SELF_REFERENCE",
			"service": "lenskiss"
		}
	 ]

	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	// "vars": { "MY_VARIABLE": "production_value" },
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */

	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },

	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
